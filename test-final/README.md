# Website Clone

This ZIP file contains a complete offline copy of: https://mvalencia464-s-s-imp-jeg0.bolt.host/

## How to View:

### Option 1: Local HTTP Server (Recommended)
To avoid CORS issues with modern browsers, serve the files through a local server:

**Using Python:**
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**Using Node.js:**
```bash
npx serve .
# or
npx http-server
```

**Using PHP:**
```bash
php -S localhost:8000
```

Then open: http://localhost:8000

### Option 2: Direct File Access
1. Extract this ZIP file to a folder
2. Open index.html in your web browser
3. Navigate through the site using the links

**Note:** If you see CORS errors, use Option 1 instead.

## Contents:
- HTML pages from the original website
- CSS stylesheets in assets/css/
- JavaScript files in assets/js/
- Images in assets/images/
- Fonts and other assets in assets/fonts/
- website-content.md - All text content in markdown format for AI analysis

## Note:
Some dynamic functionality may not work in this offline copy.
External links will still point to the original websites.

The website-content.md file contains all extracted text content from the website
in a clean markdown format, perfect for AI tools and content analysis.

Generated on: 2025-09-20T17:52:45.827Z
