/*! * Snowplow - The world's most powerful web analytics platform
 *
 * @description JavaScript tracker for Snow<PERSON>low
 * @version     2.0.0
 * <AUTHOR> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>
 * @copyright   An<PERSON><PERSON>, Snowplow Analytics Ltd
 * @license     Simplified BSD
 */
(function e(b,g,d){function c(l,i){if(!g[l]){if(!b[l]){var h=typeof require=="function"&&require;if(!i&&h){return h(l,!0)}if(a){return a(l,!0)}throw new Error("Cannot find module '"+l+"'")}var j=g[l]={exports:{}};b[l][0].call(j.exports,function(m){var o=b[l][1][m];return c(o?o:m)},j,j.exports,e,b,g,d)}return g[l].exports}var a=typeof require=="function"&&require;for(var f=0;f<d.length;f++){c(d[f])}return c})({1:[function(require,module,exports){var JSON;if(!JSON){JSON={}}(function(){var global=Function("return this")(),JSON=global.JSON;if(!JSON){JSON={}}function f(n){return n<10?"0"+n:n}if(typeof Date.prototype.toJSON!=="function"){Date.prototype.toJSON=function(key){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null
};String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(key){return this.valueOf()}}var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;function quote(string){escapable.lastIndex=0;return escapable.test(string)?'"'+string.replace(escapable,function(a){var c=meta[a];return typeof c==="string"?c:"\\u"+("0000"+a.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+string+'"'}function str(key,holder){var i,k,v,length,mind=gap,partial,value=holder[key];if(value&&typeof value==="object"&&typeof value.toJSON==="function"){value=value.toJSON(key)}if(typeof rep==="function"){value=rep.call(holder,key,value)}switch(typeof value){case"string":return quote(value);case"number":return isFinite(value)?String(value):"null";
case"boolean":case"null":return String(value);case"object":if(!value){return"null"}gap+=indent;partial=[];if(Object.prototype.toString.apply(value)==="[object Array]"){length=value.length;for(i=0;i<length;i+=1){partial[i]=str(i,value)||"null"}v=partial.length===0?"[]":gap?"[\n"+gap+partial.join(",\n"+gap)+"\n"+mind+"]":"["+partial.join(",")+"]";gap=mind;return v}if(rep&&typeof rep==="object"){length=rep.length;for(i=0;i<length;i+=1){if(typeof rep[i]==="string"){k=rep[i];v=str(k,value);if(v){partial.push(quote(k)+(gap?": ":":")+v)}}}}else{for(k in value){if(Object.prototype.hasOwnProperty.call(value,k)){v=str(k,value);if(v){partial.push(quote(k)+(gap?": ":":")+v)}}}}v=partial.length===0?"{}":gap?"{\n"+gap+partial.join(",\n"+gap)+"\n"+mind+"}":"{"+partial.join(",")+"}";gap=mind;return v}}if(typeof JSON.stringify!=="function"){JSON.stringify=function(value,replacer,space){var i;gap="";indent="";if(typeof space==="number"){for(i=0;i<space;i+=1){indent+=" "}}else{if(typeof space==="string"){indent=space
}}rep=replacer;if(replacer&&typeof replacer!=="function"&&(typeof replacer!=="object"||typeof replacer.length!=="number")){throw new Error("JSON.stringify")}return str("",{"":value})}}if(typeof JSON.parse!=="function"){JSON.parse=function(text,reviver){var j;function walk(holder,key){var k,v,value=holder[key];if(value&&typeof value==="object"){for(k in value){if(Object.prototype.hasOwnProperty.call(value,k)){v=walk(value,k);if(v!==undefined){value[k]=v}else{delete value[k]}}}}return reviver.call(holder,key,value)}text=String(text);cx.lastIndex=0;if(cx.test(text)){text=text.replace(cx,function(a){return"\\u"+("0000"+a.charCodeAt(0).toString(16)).slice(-4)})}if(/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,""))){j=eval("("+text+")");return typeof reviver==="function"?walk({"":j},""):j}throw new SyntaxError("JSON.parse")}}global.JSON=JSON;module.exports=JSON
}())},{}],2:[function(b,c,a){this.cookie=function(f,h,d,j,g,i){if(arguments.length>1){return document.cookie=f+"="+escape(h)+(d?"; expires="+new Date(+new Date()+(d*1000)).toUTCString():"")+(j?"; path="+j:"")+(g?"; domain="+g:"")+(i?"; secure":"")}return unescape((("; "+document.cookie).split("; "+f+"=")[1]||"").split(";")[0])}},{}],3:[function(b,c,a){(function(d){var f=(function(){var h="s",i=function(q){var r=-q.getTimezoneOffset();return(r!==null?r:0)},m=function(r,s,q){var t=new Date();if(r!==undefined){t.setFullYear(r)}t.setMonth(s);t.setDate(q);return t},j=function(q){return i(m(q,0,2))},n=function(q){return i(m(q,5,2))},g=function(r){var s=r.getMonth()>7,v=s?n(r.getFullYear()):j(r.getFullYear()),q=i(r),u=v<0,t=v-q;if(!u&&!s){return t<0}return t!==0},l=function(){var q=j(),r=n(),s=q-r;if(s<0){return q+",1"}else{if(s>0){return r+",1,"+h}}return q+",0"},o=function(){var q=l();return new f.TimeZone(f.olson.timezones[q])},p=function(q){var r=new Date(2010,6,15,1,0,0,0),s={"America/Denver":new Date(2011,2,13,3,0,0,0),"America/Mazatlan":new Date(2011,3,3,3,0,0,0),"America/Chicago":new Date(2011,2,13,3,0,0,0),"America/Mexico_City":new Date(2011,3,3,3,0,0,0),"America/Asuncion":new Date(2012,9,7,3,0,0,0),"America/Santiago":new Date(2012,9,3,3,0,0,0),"America/Campo_Grande":new Date(2012,9,21,5,0,0,0),"America/Montevideo":new Date(2011,9,2,3,0,0,0),"America/Sao_Paulo":new Date(2011,9,16,5,0,0,0),"America/Los_Angeles":new Date(2011,2,13,8,0,0,0),"America/Santa_Isabel":new Date(2011,3,5,8,0,0,0),"America/Havana":new Date(2012,2,10,2,0,0,0),"America/New_York":new Date(2012,2,10,7,0,0,0),"Europe/Helsinki":new Date(2013,2,31,5,0,0,0),"Pacific/Auckland":new Date(2011,8,26,7,0,0,0),"America/Halifax":new Date(2011,2,13,6,0,0,0),"America/Goose_Bay":new Date(2011,2,13,2,1,0,0),"America/Miquelon":new Date(2011,2,13,5,0,0,0),"America/Godthab":new Date(2011,2,27,1,0,0,0),"Europe/Moscow":r,"Asia/Amman":new Date(2013,2,29,1,0,0,0),"Asia/Beirut":new Date(2013,2,31,2,0,0,0),"Asia/Damascus":new Date(2013,3,6,2,0,0,0),"Asia/Jerusalem":new Date(2013,2,29,5,0,0,0),"Asia/Yekaterinburg":r,"Asia/Omsk":r,"Asia/Krasnoyarsk":r,"Asia/Irkutsk":r,"Asia/Yakutsk":r,"Asia/Vladivostok":r,"Asia/Baku":new Date(2013,2,31,4,0,0),"Asia/Yerevan":new Date(2013,2,31,3,0,0),"Asia/Kamchatka":r,"Asia/Gaza":new Date(2010,2,27,4,0,0),"Africa/Cairo":new Date(2010,4,1,3,0,0),"Europe/Minsk":r,"Pacific/Apia":new Date(2010,10,1,1,0,0,0),"Pacific/Fiji":new Date(2010,11,1,0,0,0),"Australia/Perth":new Date(2008,10,1,1,0,0,0)};
return s[q]};return{determine:o,date_is_dst:g,dst_start_for:p}}());f.TimeZone=function(g){var h={"America/Denver":["America/Denver","America/Mazatlan"],"America/Chicago":["America/Chicago","America/Mexico_City"],"America/Santiago":["America/Santiago","America/Asuncion","America/Campo_Grande"],"America/Montevideo":["America/Montevideo","America/Sao_Paulo"],"Asia/Beirut":["Asia/Amman","Asia/Jerusalem","Asia/Beirut","Europe/Helsinki","Asia/Damascus"],"Pacific/Auckland":["Pacific/Auckland","Pacific/Fiji"],"America/Los_Angeles":["America/Los_Angeles","America/Santa_Isabel"],"America/New_York":["America/Havana","America/New_York"],"America/Halifax":["America/Goose_Bay","America/Halifax"],"America/Godthab":["America/Miquelon","America/Godthab"],"Asia/Dubai":["Europe/Moscow"],"Asia/Dhaka":["Asia/Yekaterinburg"],"Asia/Jakarta":["Asia/Omsk"],"Asia/Shanghai":["Asia/Krasnoyarsk","Australia/Perth"],"Asia/Tokyo":["Asia/Irkutsk"],"Australia/Brisbane":["Asia/Yakutsk"],"Pacific/Noumea":["Asia/Vladivostok"],"Pacific/Tarawa":["Asia/Kamchatka","Pacific/Fiji"],"Pacific/Tongatapu":["Pacific/Apia"],"Asia/Baghdad":["Europe/Minsk"],"Asia/Baku":["Asia/Yerevan","Asia/Baku"],"Africa/Johannesburg":["Asia/Gaza","Africa/Cairo"]},i=g,l=function(){var m=h[i],o=m.length,n=0,p=m[0];
for(;n<o;n+=1){p=m[n];if(f.date_is_dst(f.dst_start_for(p))){i=p;return}}},j=function(){return typeof(h[i])!=="undefined"};if(j()){l()}return{name:function(){return i}}};f.olson={};f.olson.timezones={"-720,0":"Pacific/Majuro","-660,0":"Pacific/Pago_Pago","-600,1":"America/Adak","-600,0":"Pacific/Honolulu","-570,0":"Pacific/Marquesas","-540,0":"Pacific/Gambier","-540,1":"America/Anchorage","-480,1":"America/Los_Angeles","-480,0":"Pacific/Pitcairn","-420,0":"America/Phoenix","-420,1":"America/Denver","-360,0":"America/Guatemala","-360,1":"America/Chicago","-360,1,s":"Pacific/Easter","-300,0":"America/Bogota","-300,1":"America/New_York","-270,0":"America/Caracas","-240,1":"America/Halifax","-240,0":"America/Santo_Domingo","-240,1,s":"America/Santiago","-210,1":"America/St_Johns","-180,1":"America/Godthab","-180,0":"America/Argentina/Buenos_Aires","-180,1,s":"America/Montevideo","-120,0":"America/Noronha","-120,1":"America/Noronha","-60,1":"Atlantic/Azores","-60,0":"Atlantic/Cape_Verde","0,0":"UTC","0,1":"Europe/London","60,1":"Europe/Berlin","60,0":"Africa/Lagos","60,1,s":"Africa/Windhoek","120,1":"Asia/Beirut","120,0":"Africa/Johannesburg","180,0":"Asia/Baghdad","180,1":"Europe/Moscow","210,1":"Asia/Tehran","240,0":"Asia/Dubai","240,1":"Asia/Baku","270,0":"Asia/Kabul","300,1":"Asia/Yekaterinburg","300,0":"Asia/Karachi","330,0":"Asia/Kolkata","345,0":"Asia/Kathmandu","360,0":"Asia/Dhaka","360,1":"Asia/Omsk","390,0":"Asia/Rangoon","420,1":"Asia/Krasnoyarsk","420,0":"Asia/Jakarta","480,0":"Asia/Shanghai","480,1":"Asia/Irkutsk","525,0":"Australia/Eucla","525,1,s":"Australia/Eucla","540,1":"Asia/Yakutsk","540,0":"Asia/Tokyo","570,0":"Australia/Darwin","570,1,s":"Australia/Adelaide","600,0":"Australia/Brisbane","600,1":"Asia/Vladivostok","600,1,s":"Australia/Sydney","630,1,s":"Australia/Lord_Howe","660,1":"Asia/Kamchatka","660,0":"Pacific/Noumea","690,0":"Pacific/Norfolk","720,1,s":"Pacific/Auckland","720,0":"Pacific/Tarawa","765,1,s":"Pacific/Chatham","780,0":"Pacific/Tongatapu","780,1,s":"Pacific/Apia","840,0":"Pacific/Kiritimati"};
if(typeof a!=="undefined"){a.jstz=f}else{d.jstz=f}})(this)},{}],4:[function(b,c,a){(function(){var i=this;function g(q,m){var j=q.length,p=m^j,o=0,n;while(j>=4){n=((q.charCodeAt(o)&255))|((q.charCodeAt(++o)&255)<<8)|((q.charCodeAt(++o)&255)<<16)|((q.charCodeAt(++o)&255)<<24);n=(((n&65535)*1540483477)+((((n>>>16)*1540483477)&65535)<<16));n^=n>>>24;n=(((n&65535)*1540483477)+((((n>>>16)*1540483477)&65535)<<16));p=(((p&65535)*1540483477)+((((p>>>16)*1540483477)&65535)<<16))^n;j-=4;++o}switch(j){case 3:p^=(q.charCodeAt(o+2)&255)<<16;case 2:p^=(q.charCodeAt(o+1)&255)<<8;case 1:p^=(q.charCodeAt(o)&255);p=(((p&65535)*1540483477)+((((p>>>16)*1540483477)&65535)<<16))}p^=p>>>13;p=(((p&65535)*1540483477)+((((p>>>16)*1540483477)&65535)<<16));p^=p>>>15;return p>>>0}function f(t,p){var u,v,r,l,o,j,m,s,q,n;u=t.length&3;v=t.length-u;r=p;o=3432918353;m=461845907;n=0;while(n<v){q=((t.charCodeAt(n)&255))|((t.charCodeAt(++n)&255)<<8)|((t.charCodeAt(++n)&255)<<16)|((t.charCodeAt(++n)&255)<<24);++n;q=((((q&65535)*o)+((((q>>>16)*o)&65535)<<16)))&4294967295;
q=(q<<15)|(q>>>17);q=((((q&65535)*m)+((((q>>>16)*m)&65535)<<16)))&4294967295;r^=q;r=(r<<13)|(r>>>19);l=((((r&65535)*5)+((((r>>>16)*5)&65535)<<16)))&4294967295;r=(((l&65535)+27492)+((((l>>>16)+58964)&65535)<<16))}q=0;switch(u){case 3:q^=(t.charCodeAt(n+2)&255)<<16;case 2:q^=(t.charCodeAt(n+1)&255)<<8;case 1:q^=(t.charCodeAt(n)&255);q=(((q&65535)*o)+((((q>>>16)*o)&65535)<<16))&4294967295;q=(q<<15)|(q>>>17);q=(((q&65535)*m)+((((q>>>16)*m)&65535)<<16))&4294967295;r^=q}r^=t.length;r^=r>>>16;r=(((r&65535)*2246822507)+((((r>>>16)*2246822507)&65535)<<16))&4294967295;r^=r>>>13;r=((((r&65535)*3266489909)+((((r>>>16)*3266489909)&65535)<<16)))&4294967295;r^=r>>>16;return r>>>0}var d=f;d.v2=g;d.v3=f;if(typeof(c)!="undefined"){c.exports=d}else{var h=i.murmur;d.noConflict=function(){i.murmur=h;return d};i.murmur=d}}())},{}],5:[function(c,d,b){var a={utf8:{stringToBytes:function(f){return a.bin.stringToBytes(unescape(encodeURIComponent(f)))},bytesToString:function(f){return decodeURIComponent(escape(a.bin.bytesToString(f)))
}},bin:{stringToBytes:function(h){for(var f=[],g=0;g<h.length;g++){f.push(h.charCodeAt(g)&255)}return f},bytesToString:function(f){for(var h=[],g=0;g<f.length;g++){h.push(String.fromCharCode(f[g]))}return h.join("")}}};d.exports=a},{}],6:[function(b,c,a){(function(){var d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f={rotl:function(h,g){return(h<<g)|(h>>>(32-g))},rotr:function(h,g){return(h<<(32-g))|(h>>>g)},endian:function(h){if(h.constructor==Number){return f.rotl(h,8)&16711935|f.rotl(h,24)&4278255360}for(var g=0;g<h.length;g++){h[g]=f.endian(h[g])}return h},randomBytes:function(h){for(var g=[];h>0;h--){g.push(Math.floor(Math.random()*256))}return g},bytesToWords:function(h){for(var l=[],j=0,g=0;j<h.length;j++,g+=8){l[g>>>5]|=h[j]<<(24-g%32)}return l},wordsToBytes:function(i){for(var h=[],g=0;g<i.length*32;g+=8){h.push((i[g>>>5]>>>(24-g%32))&255)}return h},bytesToHex:function(g){for(var j=[],h=0;h<g.length;h++){j.push((g[h]>>>4).toString(16));j.push((g[h]&15).toString(16))
}return j.join("")},hexToBytes:function(h){for(var g=[],i=0;i<h.length;i+=2){g.push(parseInt(h.substr(i,2),16))}return g},bytesToBase64:function(h){for(var g=[],m=0;m<h.length;m+=3){var n=(h[m]<<16)|(h[m+1]<<8)|h[m+2];for(var l=0;l<4;l++){if(m*8+l*6<=h.length*8){g.push(d.charAt((n>>>6*(3-l))&63))}else{g.push("=")}}}return g.join("")},base64ToBytes:function(h){h=h.replace(/[^A-Z0-9+\/]/ig,"");for(var g=[],j=0,l=0;j<h.length;l=++j%4){if(l==0){continue}g.push(((d.indexOf(h.charAt(j-1))&(Math.pow(2,-2*l+8)-1))<<(l*2))|(d.indexOf(h.charAt(j))>>>(6-l*2)))}return g}};c.exports=f})()},{}],7:[function(b,c,a){(function(){var h=b("crypt"),d=b("charenc").utf8,f=b("charenc").bin,i=function(r){if(r.constructor==String){r=d.stringToBytes(r)}var z=h.bytesToWords(r),A=r.length*8,s=[],v=1732584193,u=-271733879,q=-1732584194,p=271733878,o=-1009589776;z[A>>5]|=128<<(24-A%32);z[((A+64>>>9)<<4)+15]=A;for(var C=0;C<z.length;C+=16){var H=v,G=u,F=q,E=p,D=o;for(var B=0;B<80;B++){if(B<16){s[B]=z[C+B]}else{var y=s[B-3]^s[B-8]^s[B-14]^s[B-16];
s[B]=(y<<1)|(y>>>31)}var x=((v<<5)|(v>>>27))+o+(s[B]>>>0)+(B<20?(u&q|~u&p)+1518500249:B<40?(u^q^p)+1859775393:B<60?(u&q|u&p|q&p)-1894007588:(u^q^p)-899497514);o=p;p=q;q=(u<<30)|(u>>>2);u=v;v=x}v+=H;u+=G;q+=F;p+=E;o+=D}return[v,u,q,p,o]},g=function(m,j){var l=h.wordsToBytes(i(m));return j&&j.asBytes?l:j&&j.asString?f.bytesToString(l):h.bytesToHex(l)};g._blocksize=16;g._digestsize=20;c.exports=g})()},{charenc:5,crypt:6}],8:[function(b,c,a){(function(){var g=b("./lib_managed/lodash"),f=b("./lib/helpers"),d=typeof a!=="undefined"?a:this;d.InQueueManager=function(h,q,p,m,r){var j={},n={};function t(x){var y=[];if(!x||x.length===0){y=g.map(j)}else{for(var w=0;w<x.length;w++){if(j.hasOwnProperty(x[w])){y.push(j[x[w]])}else{f.warn('Warning: Tracker namespace "'+x[w]+'" not configured')}}}if(y.length===0){f.warn("Warning: No tracker configured")}return y}function l(x,y,w){f.warn(x+" is deprecated. Set the collector when a new tracker instance using newTracker.");var i;if(g.isUndefined(w)){i="sp"
}else{i=w}s(i);j[i][x](y)}function s(w,x,i){i=i||{};if((!i.writeCookies)&&(i.cookieName in n)){i.writeCookies=false}else{n[i.cookieName]=true}j[w]=new h(r,w,q,p,i);j[w].setCollectorUrl(x)}function v(y){var x=y.split(":"),i=x[0],w=(x.length>1)?x[1].split(";"):[];return[i,w]}function u(){var x,z,y,C,D,A,B;for(x=0;x<arguments.length;x+=1){y=arguments[x];C=Array.prototype.shift.call(y);D=v(C);z=D[0];A=D[1];if(z==="newTracker"){s(y[0],y[1],y[2]);continue}if((z==="setCollectorCf"||z==="setCollectorUrl")&&(!A||A.length===0)){l(z,y[0],y[1]);continue}B=t(A);if(g.isString(z)){for(var w=0;w<B.length;w++){B[w][z].apply(B[w],y)}}else{for(var w=0;w<B.length;w++){z.apply(B[w],y)}}}}for(var o=0;o<m.length;o++){u(m[o])}return{push:u}}}())},{"./lib/helpers":12,"./lib_managed/lodash":14}],9:[function(d,f,b){var h=d("./snowplow"),g,a,c=window;if(c.GlobalSnowplowNamespace&&c.GlobalSnowplowNamespace.length>0){g=c.GlobalSnowplowNamespace.shift();a=c[g];a.q=new h.Snowplow(a.q,g)}else{c._snaq=c._snaq||[];c._snaq=new h.Snowplow(c._snaq,"_snaq")
}},{"./snowplow":17}],10:[function(b,c,a){(function(){var d=typeof a!=="undefined"?a:this;function f(q){var m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var l,j,h,v,u,t,s,w,p=0,x=0,o="",n=[];if(!q){return q}q=unescape(encodeURIComponent(q));do{l=q.charCodeAt(p++);j=q.charCodeAt(p++);h=q.charCodeAt(p++);w=l<<16|j<<8|h;v=w>>18&63;u=w>>12&63;t=w>>6&63;s=w&63;n[x++]=m.charAt(v)+m.charAt(u)+m.charAt(t)+m.charAt(s)}while(p<q.length);o=n.join("");var g=q.length%3;return(g?o.slice(0,g-3):o)+"===".slice(g||3)}d.base64encode=f}())},{}],11:[function(b,c,a){(function(){var o=b("../lib_managed/lodash"),d=b("./helpers"),n=b("murmurhash").v3,i=b("jstimezonedetect").jstz.determine(),g=b("browser-cookie-lite"),j=typeof a!=="undefined"?a:this,m=window,f=navigator,l=screen,h=document;j.hasSessionStorage=function(){try{return !!m.sessionStorage}catch(p){return true}};j.hasLocalStorage=function(){try{return !!m.localStorage}catch(p){return true}};j.localStorageAccessible=function(){var p="modernizr";
if(!j.hasLocalStorage()){return false}try{m.localStorage.setItem(p,p);m.localStorage.removeItem(p);return true}catch(q){return false}};j.hasCookies=function(p){var q=p||"testcookie";if(o.isUndefined(f.cookieEnabled)){g.cookie(q,"1");return g.cookie(q)==="1"?"1":"0"}return f.cookieEnabled?"1":"0"};j.detectSignature=function(u){var s=[f.userAgent,[l.height,l.width,l.colorDepth].join("x"),(new Date()).getTimezoneOffset(),j.hasSessionStorage(),j.hasLocalStorage()];var p=[];if(f.plugins){for(var t=0;t<f.plugins.length;t++){var q=[];for(var r=0;r<f.plugins[t].length;r++){q.push([f.plugins[t][r].type,f.plugins[t][r].suffixes])}p.push([f.plugins[t].name+"::"+f.plugins[t].description,q.join("~")])}}return n(s.join("###")+"###"+p.sort().join(";"),u)};j.detectTimezone=function(){return(typeof(i)==="undefined")?"":i.name()};j.detectViewport=function(){var q=m,p="inner";if(!("innerWidth" in m)){p="client";q=h.documentElement||h.body}return q[p+"Width"]+"x"+q[p+"Height"]};j.detectDocumentSize=function(){var r=h.documentElement;
var p=Math.max(r.clientWidth,r.offsetWidth,r.scrollWidth);var q=Math.max(r.clientHeight,r.offsetHeight,r.scrollHeight);return isNaN(p)||isNaN(q)?"":p+"x"+q};j.detectBrowserFeatures=function(q){var p,s,t={pdf:"application/pdf",qt:"video/quicktime",realp:"audio/x-pn-realaudio-plugin",wma:"application/x-mplayer2",dir:"application/x-director",fla:"application/x-shockwave-flash",java:"application/x-java-vm",gears:"application/x-googlegears",ag:"application/x-silverlight"},r={};if(f.mimeTypes&&f.mimeTypes.length){for(p in t){if(Object.prototype.hasOwnProperty.call(t,p)){s=f.mimeTypes[t[p]];r[p]=(s&&s.enabledPlugin)?"1":"0"}}}if(typeof f.javaEnabled!=="unknown"&&!o.isUndefined(f.javaEnabled)&&f.javaEnabled()){r.java="1"}if(o.isFunction(m.GearsFactory)){r.gears="1"}r.res=l.width+"x"+l.height;r.cd=l.colorDepth;r.cookie=j.hasCookies(q);return r}}())},{"../lib_managed/lodash":14,"./helpers":12,"browser-cookie-lite":2,jstimezonedetect:3,murmurhash:4}],12:[function(b,c,a){(function(){var f=b("../lib_managed/lodash"),d=typeof a!=="undefined"?a:this;
d.fixupTitle=function(h){if(!f.isString(h)){h=h.text||"";var g=document.getElementsByTagName("title");if(g&&!f.isUndefined(g[0])){h=g[0].text}}return h};d.getHostName=function(g){var i=new RegExp("^(?:(?:https?|ftp):)/*(?:[^@]+@)?([^:/#]+)"),h=i.exec(g);return h?h[1]:g};d.fixupDomain=function(h){var g=h.length;if(h.charAt(--g)==="."){h=h.slice(0,g)}if(h.slice(0,2)==="*."){h=h.slice(1)}return h};d.getReferrer=function(){var h="";var g=d.fromQuerystring("referrer",window.location.href)||d.fromQuerystring("referer",window.location.href);if(g){return g}try{h=window.top.document.referrer}catch(j){if(window.parent){try{h=window.parent.document.referrer}catch(i){h=""}}}if(h===""){h=document.referrer}return h};d.addEventListener=function(j,i,h,g){if(j.addEventListener){j.addEventListener(i,h,g);return true}if(j.attachEvent){return j.attachEvent("on"+i,h)}j["on"+i]=h};d.fromQuerystring=function(i,h){var g=RegExp("^[^#]*[?&]"+i+"=([^&#]*)").exec(h);if(!g){return null}return decodeURIComponent(g[1].replace(/\+/g," "))
};d.deleteEmptyProperties=function(h){for(var g in h){if(h.hasOwnProperty(g)&&(f.isUndefined(h[g])||f.isNull(h[g])||h[g].length===0)){delete h[g]}}return h};d.warn=function(g){if(typeof console!==undefined){console.warn("Snowplow: "+g)}}}())},{"../lib_managed/lodash":14}],13:[function(b,c,a){(function(){var g=b("./helpers"),d=typeof a!=="undefined"?a:this;function i(l){var j=new RegExp("^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");return j.test(l)}function f(n){var l,j;if(i(n)){try{l=document.body.children[0].children[0].children[0].children[0].children[0].children[0].innerHTML;j="You have reached the cached page for";return l.slice(0,j.length)===j}catch(m){return false}}}function h(m,l){var o=new RegExp("^(?:https?|ftp)(?::/*(?:[^?]+))([?][^#]+)"),n=o.exec(m),j=g.fromQuerystring(l,n[1]);return j}d.fixupUrl=function(m,j,l){if(m==="translate.googleusercontent.com"){if(l===""){l=j
}j=h(j,"u");m=g.getHostName(j)}else{if(m==="cc.bingj.com"||m==="webcache.googleusercontent.com"||f(m)){j=document.links[0].href;m=g.getHostName(j)}}return[m,j,l]}}())},{"./helpers":12}],14:[function(b,c,a){(function(d){(function(){var ab=[];var W={};var T=40;var aa=/^\s*function[ \n\r\t]+\w/;var h=/\bthis\b/;var H=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"];var j="[object Arguments]",F="[object Array]",ag="[object Boolean]",r="[object Date]",aB="[object Error]",u="[object Function]",ak="[object Number]",am="[object Object]",ao="[object RegExp]",O="[object String]";var ay={configurable:false,enumerable:false,value:null,writable:false};var R={args:"",array:null,bottom:"",firstArg:"",init:"",keys:null,loop:"",shadowedProps:null,support:null,top:"",useHas:false};var af={"boolean":false,"function":true,object:true,number:false,string:false,"undefined":false};var at=(af[typeof window]&&window)||this;var v=af[typeof a]&&a&&!a.nodeType&&a;
var K=af[typeof c]&&c&&!c.nodeType&&c;var aD=K&&K.exports===v&&v;var ah=af[typeof d]&&d;if(ah&&(ah.global===ah||ah.window===ah)){at=ah}function aF(){return ab.pop()||[]}function P(aI){return typeof aI.toString!="function"&&typeof(aI+"")=="string"}function ap(aI){aI.length=0;if(ab.length<T){ab.push(aI)}}function z(aN,aM,aJ){aM||(aM=0);if(typeof aJ=="undefined"){aJ=aN?aN.length:0}var aK=-1,aL=aJ-aM||0,aI=Array(aL<0?0:aL);while(++aK<aL){aI[aK]=aN[aM+aK]}return aI}var n=[];var l=Error.prototype,av=Object.prototype,V=String.prototype;var ae=av.toString;var p=RegExp("^"+String(ae).replace(/[.*+?^${}()|[\]\\]/g,"\\$&").replace(/toString| for [^\]]+/g,".*?")+"$");var y=Function.prototype.toString,Z=av.hasOwnProperty,i=n.push,Y=av.propertyIsEnumerable,ax=n.unshift;var w=(function(){try{var aL={},aJ=L(aJ=Object.defineProperty)&&aJ,aI=aJ(aL,aL,aL)&&aJ}catch(aK){}return aI}());var M=L(M=Object.create)&&M,t=L(t=Array.isArray)&&t,aE=L(aE=Object.keys)&&aE;var al={};al[F]=al[r]=al[ak]={constructor:true,toLocaleString:true,toString:true,valueOf:true};
al[ag]=al[O]={constructor:true,toString:true,valueOf:true};al[aB]=al[u]=al[ao]={constructor:true,toString:true};al[am]={constructor:true};(function(){var aK=H.length;while(aK--){var aI=H[aK];for(var aJ in al){if(Z.call(al,aJ)&&!Z.call(al[aJ],aI)){al[aJ][aI]=false}}}}());function A(){}var aw=A.support={};(function(){var aL=function(){this.x=1},aI={"0":1,length:1},aK=[];aL.prototype={valueOf:1,y:1};for(var aJ in new aL){aK.push(aJ)}for(aJ in arguments){}aw.argsClass=ae.call(arguments)==j;aw.argsObject=arguments.constructor==Object&&!(arguments instanceof Array);aw.enumErrorProps=Y.call(l,"message")||Y.call(l,"name");aw.enumPrototypes=Y.call(aL,"prototype");aw.funcDecomp=!L(at.WinRTError)&&h.test(function(){return this});aw.funcNames=typeof Function.name=="string";aw.nonEnumArgs=aJ!=0;aw.nonEnumShadows=!/valueOf/.test(aK);aw.spliceObjects=(n.splice.call(aI,0,1),!aI[0]);aw.unindexedChars=("x"[0]+Object("x")[0])!="xx"}(1));var X=function(aK){var aI="var index, iterable = "+(aK.firstArg)+", result = "+(aK.init)+";\nif (!iterable) return result;\n"+(aK.top)+";";
if(aK.array){aI+="\nvar length = iterable.length; index = -1;\nif ("+(aK.array)+") {  ";if(aw.unindexedChars){aI+="\n  if (isString(iterable)) {\n    iterable = iterable.split('')\n  }  "}aI+="\n  while (++index < length) {\n    "+(aK.loop)+";\n  }\n}\nelse {  "}else{if(aw.nonEnumArgs){aI+="\n  var length = iterable.length; index = -1;\n  if (length && isArguments(iterable)) {\n    while (++index < length) {\n      index += '';\n      "+(aK.loop)+";\n    }\n  } else {  "}}if(aw.enumPrototypes){aI+="\n  var skipProto = typeof iterable == 'function';\n  "}if(aw.enumErrorProps){aI+="\n  var skipErrorProps = iterable === errorProto || iterable instanceof Error;\n  "}var aJ=[];if(aw.enumPrototypes){aJ.push('!(skipProto && index == "prototype")')}if(aw.enumErrorProps){aJ.push('!(skipErrorProps && (index == "message" || index == "name"))')}if(aK.useHas&&aK.keys){aI+="\n  var ownIndex = -1,\n      ownProps = objectTypes[typeof iterable] && keys(iterable),\n      length = ownProps ? ownProps.length : 0;\n\n  while (++ownIndex < length) {\n    index = ownProps[ownIndex];\n";
if(aJ.length){aI+="    if ("+(aJ.join(" && "))+") {\n  "}aI+=(aK.loop)+";    ";if(aJ.length){aI+="\n    }"}aI+="\n  }  "}else{aI+="\n  for (index in iterable) {\n";if(aK.useHas){aJ.push("hasOwnProperty.call(iterable, index)")}if(aJ.length){aI+="    if ("+(aJ.join(" && "))+") {\n  "}aI+=(aK.loop)+";    ";if(aJ.length){aI+="\n    }"}aI+="\n  }    ";if(aw.nonEnumShadows){aI+="\n\n  if (iterable !== objectProto) {\n    var ctor = iterable.constructor,\n        isProto = iterable === (ctor && ctor.prototype),\n        className = iterable === stringProto ? stringClass : iterable === errorProto ? errorClass : toString.call(iterable),\n        nonEnum = nonEnumProps[className];\n      ";for(k=0;k<7;k++){aI+="\n    index = '"+(aK.shadowedProps[k])+"';\n    if ((!(isProto && nonEnum[index]) && hasOwnProperty.call(iterable, index))";if(!aK.useHas){aI+=" || (!nonEnum[index] && iterable[index] !== objectProto[index])"}aI+=") {\n      "+(aK.loop)+";\n    }      "}aI+="\n  }    "}}if(aK.array||aw.nonEnumArgs){aI+="\n}"
}aI+=(aK.bottom)+";\nreturn result";return aI};function E(aM){var aL=aM[0],aJ=aM[2],aI=aM[4];function aK(){if(aJ){var aP=z(aJ);i.apply(aP,arguments)}if(this instanceof aK){var aO=s(aL.prototype),aN=aL.apply(aO,aP||arguments);return C(aN)?aN:aO}return aL.apply(aI,aP||arguments)}aC(aK,aM);return aK}function s(aI,aJ){return C(aI)?M(aI):{}}if(!M){s=(function(){function aI(){}return function(aK){if(C(aK)){aI.prototype=aK;var aJ=new aI;aI.prototype=null}return aJ||at.Object()}}())}function aH(aJ,aI,aM){if(typeof aJ!="function"){return Q}if(typeof aI=="undefined"||!("prototype" in aJ)){return aJ}var aL=aJ.__bindData__;if(typeof aL=="undefined"){if(aw.funcNames){aL=!aJ.name}aL=aL||!aw.funcDecomp;if(!aL){var aK=y.call(aJ);if(!aw.funcNames){aL=!aa.test(aK)}if(!aL){aL=h.test(aK);aC(aJ,aL)}}}if(aL===false||(aL!==true&&aL[1]&1)){return aJ}switch(aM){case 1:return function(aN){return aJ.call(aI,aN)};case 2:return function(aO,aN){return aJ.call(aI,aO,aN)};case 3:return function(aO,aN,aP){return aJ.call(aI,aO,aN,aP)
};case 4:return function(aN,aP,aO,aQ){return aJ.call(aI,aN,aP,aO,aQ)}}return au(aJ,aI)}function N(aL){var aN=aL[0],aK=aL[1],aP=aL[2],aJ=aL[3],aS=aL[4],aI=aL[5];var aM=aK&1,aU=aK&2,aR=aK&4,aQ=aK&8,aT=aN;function aO(){var aW=aM?aS:this;if(aP){var aX=z(aP);i.apply(aX,arguments)}if(aJ||aR){aX||(aX=z(arguments));if(aJ){i.apply(aX,aJ)}if(aR&&aX.length<aI){aK|=16&~32;return N([aN,(aQ?aK:aK&~3),aX,null,aS,aI])}}aX||(aX=arguments);if(aU){aN=aW[aT]}if(this instanceof aO){aW=s(aN.prototype);var aV=aN.apply(aW,aX);return C(aV)?aV:aW}return aN.apply(aW,aX)}aC(aO,aL);return aO}function ar(a0,aZ,aP,aW,a2,a1){if(aP){var aU=aP(a0,aZ);if(typeof aU!="undefined"){return !!aU}}if(a0===aZ){return a0!==0||(1/a0==1/aZ)}var aO=typeof a0,aM=typeof aZ;if(a0===a0&&!(a0&&af[aO])&&!(aZ&&af[aM])){return false}if(a0==null||aZ==null){return a0===aZ}var aJ=ae.call(a0),aS=ae.call(aZ);if(aJ==j){aJ=am}if(aS==j){aS=am}if(aJ!=aS){return false}switch(aJ){case ag:case r:return +a0==+aZ;case ak:return(a0!=+a0)?aZ!=+aZ:(a0==0?(1/a0==1/aZ):a0==+aZ);
case ao:case O:return a0==String(aZ)}var aQ=aJ==F;if(!aQ){var aV=Z.call(a0,"__wrapped__"),aI=Z.call(aZ,"__wrapped__");if(aV||aI){return ar(aV?a0.__wrapped__:a0,aI?aZ.__wrapped__:aZ,aP,aW,a2,a1)}if(aJ!=am){return false}var aN=!aw.argsObject&&g(a0)?Object:a0.constructor,aK=!aw.argsObject&&g(aZ)?Object:aZ.constructor;if(aN!=aK&&!(aq(aN)&&aN instanceof aN&&aq(aK)&&aK instanceof aK)&&("constructor" in a0&&"constructor" in aZ)){return false}}var aT=!a2;a2||(a2=aF());a1||(a1=aF());var aL=a2.length;while(aL--){if(a2[aL]==a0){return a1[aL]==aZ}}var aX=0;aU=true;a2.push(a0);a1.push(aZ);if(aQ){aL=a0.length;aX=aZ.length;aU=aX==aL;if(aU||aW){while(aX--){var aR=aL,aY=aZ[aX];if(aW){while(aR--){if((aU=ar(a0[aR],aY,aP,aW,a2,a1))){break}}}else{if(!(aU=ar(a0[aX],aY,aP,aW,a2,a1))){break}}}}}else{ac(aZ,function(a5,a4,a3){if(Z.call(a3,a4)){aX++;return(aU=Z.call(a0,a4)&&ar(a0[a4],a5,aP,aW,a2,a1))}});if(aU&&!aW){ac(a0,function(a5,a4,a3){if(Z.call(a3,a4)){return(aU=--aX>-1)}})}}a2.pop();a1.pop();if(aT){ap(a2);ap(a1)
}return aU}function D(aO,aL,aP,aK,aU,aI){var aN=aL&1,aV=aL&2,aS=aL&4,aR=aL&8,aJ=aL&16,aQ=aL&32;if(!aV&&!aq(aO)){throw new TypeError}if(aJ&&!aP.length){aL&=~16;aJ=aP=false}if(aQ&&!aK.length){aL&=~32;aQ=aK=false}var aM=aO&&aO.__bindData__;if(aM&&aM!==true){aM=z(aM);if(aM[2]){aM[2]=z(aM[2])}if(aM[3]){aM[3]=z(aM[3])}if(aN&&!(aM[1]&1)){aM[4]=aU}if(!aN&&aM[1]&1){aL|=8}if(aS&&!(aM[1]&4)){aM[5]=aI}if(aJ){i.apply(aM[2]||(aM[2]=[]),aP)}if(aQ){ax.apply(aM[3]||(aM[3]=[]),aK)}aM[1]|=aL;return D.apply(null,aM)}var aT=(aL==1||aL===17)?E:N;return aT([aO,aL,aP,aK,aU,aI])}function aj(){R.shadowedProps=H;R.array=R.bottom=R.loop=R.top="";R.init="iterable";R.useHas=true;for(var aL,aK=0;aL=arguments[aK];aK++){for(var aM in aL){R[aM]=aL[aM]}}var aJ=R.args;R.firstArg=/^[^,]+/.exec(aJ)[0];var aI=Function("baseCreateCallback, errorClass, errorProto, hasOwnProperty, indicatorObject, isArguments, isArray, isString, keys, objectProto, objectTypes, nonEnumProps, stringClass, stringProto, toString","return function("+aJ+") {\n"+X(R)+"\n}");
return aI(aH,aB,l,Z,W,g,f,az,R.keys,av,af,al,O,V,ae)}function L(aI){return typeof aI=="function"&&p.test(aI)}var aC=!w?an:function(aI,aJ){ay.value=aJ;w(aI,"__bindData__",ay)};function g(aI){return aI&&typeof aI=="object"&&typeof aI.length=="number"&&ae.call(aI)==j||false}if(!aw.argsClass){g=function(aI){return aI&&typeof aI=="object"&&typeof aI.length=="number"&&Z.call(aI,"callee")&&!Y.call(aI,"callee")||false}}var f=t||function(aI){return aI&&typeof aI=="object"&&typeof aI.length=="number"&&ae.call(aI)==F||false};var S=aj({args:"object",init:"[]",top:"if (!(objectTypes[typeof object])) return result",loop:"result.push(index)"});var G=!aE?S:function(aI){if(!C(aI)){return[]}if((aw.enumPrototypes&&typeof aI=="function")||(aw.nonEnumArgs&&aI.length&&g(aI))){return S(aI)}return aE(aI)};var aA={args:"collection, callback, thisArg",top:"callback = callback && typeof thisArg == 'undefined' ? callback : baseCreateCallback(callback, thisArg, 3)",array:"typeof length == 'number'",keys:G,loop:"if (callback(iterable[index], index, collection) === false) return result"};
var ad={top:"if (!objectTypes[typeof iterable]) return result;\n"+aA.top,array:false};var I=aj(aA);var ac=aj(aA,ad,{useHas:false});var U=aj(aA,ad);function m(aI){return aI&&typeof aI=="object"&&ae.call(aI)==r||false}function q(aL){var aI=true;if(!aL){return aI}var aJ=ae.call(aL),aK=aL.length;if((aJ==F||aJ==O||(aw.argsClass?aJ==j:g(aL)))||(aJ==am&&typeof aK=="number"&&aq(aL.splice))){return !aK}U(aL,function(){return(aI=false)});return aI}function aq(aI){return typeof aI=="function"}if(aq(/x/)){aq=function(aI){return typeof aI=="function"&&ae.call(aI)==u}}function C(aI){return !!(aI&&af[typeof aI])}function aG(aI){return aI===null}function az(aI){return typeof aI=="string"||aI&&typeof aI=="object"&&ae.call(aI)==O||false}function J(aI){return typeof aI=="undefined"}function x(aM,aN,aJ){var aK=-1,aL=aM?aM.length:0,aI=Array(typeof aL=="number"?aL:0);aN=A.createCallback(aN,aJ,3);if(f(aM)){while(++aK<aL){aI[aK]=aN(aM[aK],aK,aM)}}else{I(aM,function(aP,aO,aQ){aI[++aK]=aN(aP,aO,aQ)})}return aI}function B(aM){var aJ=-1,aK=aM?aM.length:0,aI=[];
while(++aJ<aK){var aL=aM[aJ];if(aL){aI.push(aL)}}return aI}function au(aJ,aI){return arguments.length>2?D(aJ,17,z(arguments,2),null,aI):D(aJ,1,null,null,aI)}function o(aN,aJ,aO){var aM=typeof aN;if(aN==null||aM=="function"){return aH(aN,aJ,aO)}if(aM!="object"){return ai(aN)}var aL=G(aN),aK=aL[0],aI=aN[aK];if(aL.length==1&&aI===aI&&!C(aI)){return function(aQ){var aP=aQ[aK];return aI===aP&&(aI!==0||(1/aI==1/aP))}}return function(aQ){var aR=aL.length,aP=false;while(aR--){if(!(aP=ar(aQ[aL[aR]],aN[aL[aR]],null,true))){break}}return aP}}function Q(aI){return aI}function an(){}function ai(aI){return function(aJ){return aJ[aI]}}A.bind=au;A.compact=B;A.createCallback=o;A.forIn=ac;A.forOwn=U;A.keys=G;A.map=x;A.property=ai;A.collect=x;A.identity=Q;A.isArguments=g;A.isArray=f;A.isDate=m;A.isEmpty=q;A.isFunction=aq;A.isNull=aG;A.isObject=C;A.isString=az;A.isUndefined=J;A.noop=an;A.VERSION="2.4.1";if(v&&K){if(aD){(K.exports=A)._=A}}}.call(this))}).call(this,typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})
},{}],15:[function(b,c,a){(function(){var g=b("JSON"),h=b("./lib_managed/lodash"),f=b("./lib/detectors").localStorageAccessible(),d=typeof a!=="undefined"?a:this;d.OutQueueManager=function(q,l){var j=["snowplowOutQueue",q,l].join("_"),m=false,i,o;if(f){try{o=g.parse(localStorage.getItem(j))}catch(p){}}if(typeof o==="undefined"||o==null){o=[]}function r(t,s){o.push(t);i=s;if(f){localStorage.setItem(j,g.stringify(o))}if(!m){n()}}function n(){if(o.length<1){m=false;return}var t,u,s;m=true;for(s in o){if(o[s]&&o.hasOwnProperty(s)){t=o[s];if(!h.isString(i)){throw"No Snowplow collector configured, cannot track"}(function(v){var w=new Image(1,1);w.onload=function(){delete o[v];if(f){localStorage.setItem(j,g.stringify(o))}n()};w.onerror=function(){};w.src=i+t}(s))}}m=false;if(h.compact(o).length===0){o=[]}}return{enqueueRequest:r}}}())},{"./lib/detectors":11,"./lib_managed/lodash":14,JSON:1}],16:[function(b,c,a){(function(){var i=b("./lib_managed/lodash"),h=b("JSON"),d=b("./lib/base64"),g=typeof a!=="undefined"?a:this;
function f(n){if(!n){return n}var m=d.base64encode(n);return m.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function j(n,m){return m?n/1:Math.floor(n/1000)}function l(m){return Math.floor(m/86400000)}g.isJson=function(m){return(!i.isUndefined(m)&&!i.isNull(m)&&(m.constructor==={}.constructor||m.constructor===[].constructor))};g.isNonEmptyJson=function(n){if(!g.isJson(n)){return false}for(var m in n){if(n.hasOwnProperty(m)){return true}}return false};g.payloadBuilder=function(n){var r="";var m=function(t,v,u){if(v!==undefined&&v!==null&&v!==""){var s=(r.length>0)?"&":"?";r+=s+t+"="+(u?encodeURIComponent(v):v)}};var q=function(s,t){m(s,t,true)};var o=function(s,t){m(s,t,false)};var p=function(s,t,u){if(g.isNonEmptyJson(u)){var v=h.stringify(u);if(n){o(s,f(v))}else{q(t,v)}}};return{add:q,addRaw:o,addJson:p,build:function(){return r}}}}())},{"./lib/base64":10,"./lib_managed/lodash":14,JSON:1}],17:[function(b,c,a){(function(){var h=b("./lib/helpers"),d=b("./in_queue"),g=b("./tracker"),f=typeof a!=="undefined"?a:this;
f.Snowplow=function(l,q){var j=document,m=window,o="js-2.0.0",n={expireDateTime:null,hasLoaded:false,registeredOnLoadHandlers:[]};function p(){var s;if(n.expireDateTime){do{s=new Date()}while(s.getTime()<n.expireDateTime)}}function r(){var s;if(!n.hasLoaded){n.hasLoaded=true;for(s=0;s<n.registeredOnLoadHandlers.length;s++){n.registeredOnLoadHandlers[s]()}}return true}function i(){var t;if(j.addEventListener){h.addEventListener(j,"DOMContentLoaded",function s(){j.removeEventListener("DOMContentLoaded",s,false);r()})}else{if(j.attachEvent){j.attachEvent("onreadystatechange",function s(){if(j.readyState==="complete"){j.detachEvent("onreadystatechange",s);r()}});if(j.documentElement.doScroll&&m===m.top){(function s(){if(!n.hasLoaded){try{j.documentElement.doScroll("left")}catch(u){setTimeout(s,0);return}r()}}())}}}if((new RegExp("WebKit")).test(navigator.userAgent)){t=setInterval(function(){if(n.hasLoaded||/loaded|complete/.test(j.readyState)){clearInterval(t);r()}},10)}h.addEventListener(m,"load",r,false)
}m.Snowplow={getTrackerCf:function(u){var s=new g.Tracker(q,"",o,n,{});s.setCollectorCf(u);return s},getTrackerUrl:function(s){var u=new g.Tracker(q,"",o,n,{});u.setCollectorCf(s);return u},getAsyncTracker:function(){return new g.Tracker(q,"",o,n,{})}};h.addEventListener(m,"beforeunload",p,false);i();return new d.InQueueManager(g.Tracker,o,n,l,q)}}())},{"./in_queue":8,"./lib/helpers":12,"./tracker":18}],18:[function(b,c,a){(function(){var p=b("./lib_managed/lodash"),d=b("./lib/helpers"),h=b("./lib/proxies"),f=b("browser-cookie-lite"),n=b("./lib/detectors"),o=b("./payload"),i=b("JSON"),m=b("sha1"),l=b("./out_queue"),g=typeof a!=="undefined"?a:this;g.Tracker=function j(a5,aH,K,x,ao){var ah=document,Z=window,M=navigator,t=h.fixupUrl(ah.domain,Z.location.href,d.getReferrer()),aP=d.fixupDomain(t[0]),a4=t[1],aB=t[2],ao=ao||{},az="GET",T="iglu:com.snowplowanalytics.snowplow",a1=T+"/contexts/jsonschema/1-0-0",J=T+"/unstruct_event/jsonschema/1-0-0",E=ao.hasOwnProperty("platform")?ao.platform:"web",u,aS=ao.hasOwnProperty("appId")?ao.appId:"",ak,W=ah.title,ay=ao.hasOwnProperty("pageUnloadTimer")?ao.pageUnloadTimer:500,z,L,A,a2=ao.hasOwnProperty("cookieName")?ao.cookieName:"_sp_",B=ao.hasOwnProperty("cookieDomain")?ao.cookieDomain:null,a3,P=ao.hasOwnProperty("writeCookies")?ao.writeCookies:true,R=M.doNotTrack||M.msDoNotTrack,aX=ao.hasOwnProperty("respectDoNotTrack")?ao.respectDoNotTrack&&(R==="yes"||R==="1"):false,ad,F=63072000,I=1800,O=ao.hasOwnProperty("encodeBase64")?ao.encodeBase64:true,N=ao.hasOwnProperty("userFingerprintSeed")?ao.userFingerprintSeed:123412414,aV=ah.characterSet||ah.charset,D=M.userLanguage||M.language,aU=n.detectBrowserFeatures(H("testcookie")),aw=n.detectTimezone(),v=(ao.userFingerprint===false)?"":n.detectSignature(N),w,aT,af,G=a5+"_"+aH,aD=false,aA,ax,al,ai,U,at,C,Y=m,aF,ae,a7,y=aM(),aZ=new l.OutQueueManager(a5,aH);
function aC(a8){if(!p.isEmpty(a8)){return{schema:a1,data:a8}}}function aM(){return{transaction:{},items:[]}}function aY(a8){var a9;if(A){a9=new RegExp("#.*");return a8.replace(a9,"")}return a8}function a6(a8){var ba=new RegExp("^([a-z]+):"),a9=ba.exec(a8);return a9?a9[1]:null}function aO(ba,a8){var bb=a6(a8),a9;if(bb){return a8}if(a8.slice(0,1)==="/"){return a6(ba)+"://"+d.getHostName(ba)+a8}ba=aY(ba);if((a9=ba.indexOf("?"))>=0){ba=ba.slice(0,a9)}if((a9=ba.lastIndexOf("/"))!==ba.length-1){ba=ba.slice(0,a9+1)}return ba+a8}function ab(ba,a9){var a8=new Date();if(!aX){aZ.enqueueRequest(ba,u);x.expireDateTime=a8.getTime()+a9}}function H(a8){return a2+a8+"."+aF}function ac(a8){return f.cookie(H(a8))}function av(){aF=Y((B||aP)+(a3||"/")).slice(0,4)}function aR(){var a8=new Date();aA=a8.getTime()}function aK(){aj();aR()}function an(){var a8=(ah.compatMode&&ah.compatMode!="BackCompat")?ah.documentElement:ah.body;return[a8.scrollLeft||Z.pageXOffset,a8.scrollTop||Z.pageYOffset]}function ar(){var a9=an();
var a8=a9[0];ax=a8;al=a8;var ba=a9[1];ai=ba;U=ba}function aj(){var a9=an();var a8=a9[0];if(a8<ax){ax=a8}else{if(a8>al){al=a8}}var ba=a9[1];if(ba<ai){ai=ba}else{if(ba>U){U=ba}}}function a0(ba,a9,a8,bc,bb){f.cookie(H("id"),ba+"."+a9+"."+a8+"."+bc+"."+bb,F,a3,B)}function aL(){var a9=new Date(),a8=Math.round(a9.getTime()/1000),bb=ac("id"),ba;if(bb){ba=bb.split(".");ba.unshift("0")}else{if(!ae){ae=Y((M.userAgent||"")+(M.platform||"")+i.stringify(aU)+a8).slice(0,16)}ba=["1",ae,a8,0,a8,""]}return ba}function ag(){var a9=new Date(),a8=a9.getTime();return a8}function X(a8){var bq,a9=new Date(),bf=Math.round(a9.getTime()/1000),bs,bj,bc,bm,bo,be,bd,bp,bb=1024,bt,bh,bl=H("id"),bg=H("ses"),bn=aL(),bk=ac("ses"),br=ak||a4,bi;if(aX&&P){f.cookie(bl,"",-1,a3,B);f.cookie(bg,"",-1,a3,B);return""}bs=bn[0];bj=bn[1];bm=bn[2];bc=bn[3];bo=bn[4];be=bn[5];if(!bk||(parseInt(bk)<a9.getTime())){bc++;be=bo}a8.addRaw("dtm",ag());a8.addRaw("tid",String(Math.random()).slice(2,8));a8.addRaw("vp",n.detectViewport());a8.addRaw("ds",n.detectDocumentSize());
a8.addRaw("vid",bc);a8.addRaw("duid",bj);a8.add("p",E);a8.add("tv",K);a8.add("fp",v);a8.add("aid",aS);a8.add("lang",D);a8.add("cs",aV);a8.add("tz",aw);a8.add("uid",a7);a8.add("tna",aH);if(aB.length){a8.add("refr",aY(aB))}for(bq in aU){if(Object.prototype.hasOwnProperty.call(aU,bq)){bi=(bq==="res"||bq==="cd"||bq==="cookie")?"":"f_";a8.addRaw(bi+bq,aU[bq])}}a8.add("url",aY(br));var ba=a8.build();if(P){a0(bj,bm,bc,bf,be);f.cookie(bg,a9.getTime()+(I*1000),I,a3,B)}return ba}function aa(a8){return aN(a8+".cloudfront.net")}function aN(a8){return("https:"==ah.location.protocol?"https":"http")+"://"+a8+"/i"}function aq(bd,a9){var bc=d.fixupTitle(bd||W);var be=o.payloadBuilder(O);be.add("e","pv");be.add("page",bc);be.addJson("cx","co",aC(a9));var bb=X(be);ab(bb,ay);var a8=new Date();if(z&&L&&!aD){aD=true;ar();d.addEventListener(ah,"click",aR);d.addEventListener(ah,"mouseup",aR);d.addEventListener(ah,"mousedown",aR);d.addEventListener(ah,"mousemove",aR);d.addEventListener(ah,"mousewheel",aR);d.addEventListener(Z,"DOMMouseScroll",aR);
d.addEventListener(Z,"scroll",aK);d.addEventListener(ah,"keypress",aR);d.addEventListener(ah,"keydown",aR);d.addEventListener(ah,"keyup",aR);d.addEventListener(Z,"resize",aR);d.addEventListener(Z,"focus",aR);d.addEventListener(Z,"blur",aR);aA=a8.getTime();setInterval(function ba(){var bf=new Date();if((aA+L)>bf.getTime()){if(z<bf.getTime()){s(bc,a9)}}},L)}}function s(ba,a8){var bb=o.payloadBuilder(O);bb.add("e","pp");bb.add("page",ba);bb.addRaw("pp_mix",ax);bb.addRaw("pp_max",al);bb.addRaw("pp_miy",ai);bb.addRaw("pp_may",U);bb.addJson("cx","co",aC(a8));ar();var a9=X(bb);ab(a9,ay)}function Q(bb,be,a8,bd,bc,a9){var bf=o.payloadBuilder(O);bf.add("e","se");bf.add("se_ca",bb);bf.add("se_ac",be);bf.add("se_la",a8);bf.add("se_pr",bd);bf.add("se_va",bc);bf.addJson("cx","co",aC(a9));var ba=X(bf);ab(ba,ay)}function au(a8,a9){d.deleteEmptyProperties(a8.data);if(!p.isEmpty(a8.data)){var bb={schema:J,data:a8},bc=o.payloadBuilder(O);bc.add("e","ue");bc.addJson("ue_px","ue_pr",bb);bc.addJson("cx","co",aC(a9));
var ba=X(bc);ab(ba,ay)}}function aI(bd,bc,bj,be,a8,bg,a9,bb,bh,ba){var bi=o.payloadBuilder(O);bi.add("e","tr");bi.add("tr_id",bd);bi.add("tr_af",bc);bi.add("tr_tt",bj);bi.add("tr_tx",be);bi.add("tr_sh",a8);bi.add("tr_ci",bg);bi.add("tr_st",a9);bi.add("tr_co",bb);bi.add("tr_cu",bh);bi.addJson("cx","co",aC(ba));var bf=X(bi);ab(bf,ay)}function am(bb,bf,a8,a9,be,bc,bg,ba){var bh=o.payloadBuilder(O);bh.add("e","ti");bh.add("ti_id",bb);bh.add("ti_sk",bf);bh.add("ti_na",a8);bh.add("ti_ca",a9);bh.add("ti_pr",be);bh.add("ti_qu",bc);bh.add("ti_cu",bg);bh.addJson("cx","co",aC(ba));var bd=X(bh);ab(bd,ay)}function aQ(bd,a9,bb,a8,bc){var ba={schema:T+"/link_click/jsonschema/1-0-0",data:{targetUrl:bd,elementId:a9,elementClasses:bb,elementTarget:a8}};au(ba,bc)}function aE(be,a8,bb,ba,a9){var bd=o.payloadBuilder(O);bd.add("e","ad");bd.add("ad_ba",be);bd.add("ad_ca",a8);bd.add("ad_ad",bb);bd.add("ad_uid",ba);bd.addJson("cx","co",aC(a9));var bc=X(bd);ab(bc,ay)}function aW(a9,a8){if(a9!==""){return a9+a8.charAt(0).toUpperCase()+a8.slice(1)
}return a8}function S(bd){var bc,a8,bb=["","webkit","ms","moz"],ba;if(!ad){for(a8=0;a8<bb.length;a8++){ba=bb[a8];if(Object.prototype.hasOwnProperty.call(ah,aW(ba,"hidden"))){if(ah[aW(ba,"visibilityState")]==="prerender"){bc=true}break}}}if(bc){d.addEventListener(ah,ba+"visibilitychange",function a9(){ah.removeEventListener(ba+"visibilitychange",a9,false);bd()});return}bd()}function aJ(ba,a9){var bh,bi,bf,bg,be;while((bh=ba.parentNode)!==null&&!p.isUndefined(bh)&&((bi=ba.tagName.toUpperCase())!=="A"&&bi!=="AREA")){ba=bh}if(!p.isUndefined(ba.href)){var bd=ba.hostname||d.getHostName(ba.href),bb=bd.toLowerCase(),a8=ba.href.replace(bd,bb),bc=new RegExp("^(javascript|vbscript|jscript|mocha|livescript|ecmascript|mailto):","i");if(!bc.test(a8)){bf=ba.id;bg=p.map(ba.classList);be=ba.target;a8=unescape(a8);aQ(a8,bf,bg,be,a9)}}}function q(a8){return function(a9){var ba,bb;a9=a9||Z.event;ba=a9.which||a9.button;bb=a9.target||a9.srcElement;if(a9.type==="click"){if(bb){aJ(bb,a8)}}else{if(a9.type==="mousedown"){if((ba===1||ba===2)&&bb){at=ba;
C=bb}else{at=C=null}}else{if(a9.type==="mouseup"){if(ba===at&&bb===C){aJ(bb,a8)}at=C=null}}}}}function aG(a8){if(aT){d.addEventListener(a8,"mouseup",q(af),false);d.addEventListener(a8,"mousedown",q(af),false)}else{d.addEventListener(a8,"click",q(af),false)}}function ap(){var a9=ah.links,a8;for(a8=0;a8<a9.length;a8++){if(w(a9[a8])&&!a9[a8][G]){aG(a9[a8]);a9[a8][G]=true}}}function V(a9,bc){var a8=p.map(a9.classList),bb,ba;for(bb=0;bb<a8.length;bb++){for(ba=0;ba<bc.length;ba++){if(a8[bb]===bc[ba]){return true}}}return false}function r(bc,a9,ba){var bb,a8;af=ba;aT=a9;if(p.isArray(bc)||!p.isObject(bc)){w=function(bd){return true};return}if(bc.hasOwnProperty("filter")){w=bc.filter}else{a8=(bc.hasOwnProperty("whitelist"));bb=bc.whitelist||bc.blacklist;if(!p.isArray(bb)){bb=[bb]}w=function(bd){return V(bd,bb)===a8}}}av();return{getUserId:function(){return a7},getDomainUserId:function(){return(aL())[1]},getDomainUserInfo:function(){return aL()},setAppId:function(a8){d.warn('setAppId is deprecated. Instead add an "appId" field to the argmap argument of newTracker.');
aS=a8},setReferrerUrl:function(a8){aB=a8},setCustomUrl:function(a8){ak=aO(a4,a8)},setDocumentTitle:function(a8){W=a8},discardHashTag:function(a8){A=a8},setCookieNamePrefix:function(a8){d.warn('setCookieNamePrefix is deprecated. Instead add a "cookieName" field to the argmap argument of newTracker.');a2=a8},setCookieDomain:function(a8){d.warn('setCookieDomain is deprecated. Instead add a "cookieDomain" field to the argmap argument of newTracker.');B=d.fixupDomain(a8);av()},setCookiePath:function(a8){a3=a8;av()},setVisitorCookieTimeout:function(a8){F=a8},setSessionCookieTimeout:function(a8){I=a8},setUserFingerprintSeed:function(a8){d.warn('setUserFingerprintSeed is deprecated. Instead add a "userFingerprintSeed" field to the argmap argument of newTracker.');N=a8;v=n.detectSignature(N)},enableUserFingerprint:function(a8){d.warn('enableUserFingerprintSeed is deprecated. Instead add a "userFingerprint" field to the argmap argument of newTracker.');if(!a8){v=""}},respectDoNotTrack:function(a9){d.warn('This usage of respectDoNotTrack is deprecated. Instead add a "respectDoNotTrack" field to the argmap argument of newTracker.');
var a8=M.doNotTrack||M.msDoNotTrack;aX=a9&&(a8==="yes"||a8==="1")},addListener:function(ba,a8,a9){aG(ba,a8,a9)},enableLinkClickTracking:function(ba,a8,a9){if(x.hasLoaded){r(ba,a8,a9);ap()}else{x.registeredOnLoadHandlers.push(function(){r(ba,a8,a9);ap()})}},refreshLinkClickTracking:function(){ap()},enableActivityTracking:function(ba,a9){var a8=new Date();z=a8.getTime()+ba*1000;L=a9*1000},killFrame:function(){if(Z.location!==Z.top.location){Z.top.location=Z.location}},redirectFile:function(a8){if(Z.location.protocol==="file:"){Z.location=a8}},setCountPreRendered:function(a8){ad=a8},setUserId:function(a8){a7=a8},setUserIdFromLocation:function(a8){a7=d.fromQuerystring(a8,a4)},setUserIdFromReferrer:function(a8){a7=d.fromQuerystring(a8,aB)},setUserIdFromCookie:function(a8){a7=f.cookie(a8)},setCollectorCf:function(a8){u=aa(a8)},setCollectorUrl:function(a8){u=aN(a8)},setPlatform:function(a8){d.warn('setPlatform is deprecated. Instead add a "platform" field to the argmap argument of newTracker.');
E=a8},encodeBase64:function(a8){d.warn('This usage of encodeBase64 is deprecated. Instead add an "encodeBase64" field to the argmap argument of newTracker.');O=a8},trackPageView:function(a9,a8){S(function(){aq(a9,a8)})},trackStructEvent:function(ba,bd,a8,bc,bb,a9){Q(ba,bd,a8,bc,bb,a9)},trackUnstructEvent:function(a8,a9){au(a8,a9)},addTrans:function(bd,bc,bh,be,a8,bf,a9,bb,bg,ba){y.transaction={orderId:bd,affiliation:bc,total:bh,tax:be,shipping:a8,city:bf,state:a9,country:bb,currency:bg,context:ba}},addItem:function(a8,bf,ba,bd,bc,be,a9,bb){y.items.push({orderId:a8,sku:bf,name:ba,category:bd,price:bc,quantity:be,currency:a9,context:bb})},trackTrans:function(){aI(y.transaction.orderId,y.transaction.affiliation,y.transaction.total,y.transaction.tax,y.transaction.shipping,y.transaction.city,y.transaction.state,y.transaction.country,y.transaction.currency,y.transaction.context);for(var a8=0;a8<y.items.length;a8++){var a9=y.items[a8];am(a9.orderId,a9.sku,a9.name,a9.category,a9.price,a9.quantity,a9.currency,a9.context)
}y=aM()},trackLinkClick:function(bc,a9,ba,a8,bb){S(function(){aQ(bc,a9,ba,a8,bb)})},trackImpression:function(bc,a8,bb,ba,a9){d.warn("trackImpression is deprecated. When version 1.1.0 is released, switch to trackAdImpression.");aE(bc,a8,bb,ba,a9)},trackAdImpression:function(bc,a8,ba,bb,bg,bd,be,bf,a9){S(function(){var bh={schema:T+"/ad_impression/jsonschema/1-0-0",data:{impressionId:bc,costModel:a8,cost:ba,bannerId:bg,targetUrl:bb,zoneId:bd,advertiserId:be,campaignId:bf}};au(bh,a9)})},trackAdClick:function(ba,bg,a8,bb,bi,bd,bc,be,bh,a9){var bf={schema:T+"/ad_click/jsonschema/1-0-0",data:{targetUrl:ba,clickId:bg,costModel:a8,cost:bb,bannerId:bi,zoneId:bd,impressionId:bc,advertiserId:be,campaignId:bh}};au(bf,a9)},trackAdConversion:function(bi,a8,bb,ba,bd,bg,bh,bc,bf,a9){var be={schema:T+"/ad_conversion/jsonschema/1-0-0",data:{conversionId:bi,costModel:a8,cost:bb,category:ba,action:bd,property:bg,initialValue:bh,advertiserId:bc,campaignId:bf}};au(be,a9)}}}}())},{"./lib/detectors":11,"./lib/helpers":12,"./lib/proxies":13,"./lib_managed/lodash":14,"./out_queue":15,"./payload":16,JSON:1,"browser-cookie-lite":2,sha1:7}]},{},[9]);