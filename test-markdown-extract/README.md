# Website Clone

This is a local copy of a website created with StokeCloner.

## How to view:
1. Extract this ZIP file to a folder
2. Open index.html in your web browser
3. Navigate through the site using the links

## Contents:
- HTML pages from the original website
- CSS stylesheets in assets/css/
- JavaScript files in assets/js/
- Images in assets/images/
- Fonts and other assets in assets/fonts/
- website-content.md - All text content in markdown format for AI analysis

## Note:
Some dynamic functionality may not work in this offline copy.
External links will still point to the original websites.

The website-content.md file contains all extracted text content from the website
in a clean markdown format, perfect for AI tools and content analysis.

Generated on: 2025-09-20T16:34:35.779Z
