import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { promisify } from 'util';
import { exec } from 'child_process';
import AdmZip from 'adm-zip';

const execAsync = promisify(exec);

// Store active servers to avoid conflicts
const activeServers = new Map<number, { process: any; directory: string }>();

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Create a temporary directory for extraction
    const tempDir = path.join(os.tmpdir(), `stoke-clone-${Date.now()}`);
    fs.mkdirSync(tempDir, { recursive: true });

    try {
      // Save the uploaded ZIP file
      const zipPath = path.join(tempDir, 'website.zip');
      const arrayBuffer = await file.arrayBuffer();
      fs.writeFileSync(zipPath, Buffer.from(arrayBuffer));

      // Extract the ZIP file
      const zip = new AdmZip(zipPath);
      const extractDir = path.join(tempDir, 'extracted');
      zip.extractAllTo(extractDir, true);

      // Find an available port
      const port = await findAvailablePort(8000);
      
      // Start a local HTTP server
      const serverProcess = await startLocalServer(extractDir, port);
      
      // Store the server info
      activeServers.set(port, {
        process: serverProcess,
        directory: extractDir
      });

      // Clean up the ZIP file
      fs.unlinkSync(zipPath);

      // Schedule cleanup after 1 hour
      setTimeout(() => {
        cleanupServer(port);
      }, 60 * 60 * 1000); // 1 hour

      const localUrl = `http://localhost:${port}`;
      
      return NextResponse.json({
        url: localUrl,
        port,
        message: 'Local server started successfully'
      }, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        }
      });

    } catch (error) {
      // Clean up temp directory on error
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
      throw error;
    }

  } catch (error) {
    console.error('Error starting local server:', error);
    return NextResponse.json(
      { error: 'Failed to start local server' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        }
      }
    );
  }
}

async function findAvailablePort(startPort: number): Promise<number> {
  for (let port = startPort; port < startPort + 100; port++) {
    try {
      // Check if port is available
      await execAsync(`lsof -i :${port}`);
      // If no error, port is in use, try next
    } catch {
      // Error means port is available
      return port;
    }
  }
  throw new Error('No available ports found');
}

async function startLocalServer(directory: string, port: number): Promise<any> {
  return new Promise((resolve, reject) => {
    const isWindows = process.platform === 'win32';

    // Try different server options in order of preference
    const serverOptions = [
      // Python 3 (most common)
      { cmd: isWindows ? 'python' : 'python3', args: ['-m', 'http.server', port.toString()] },
      // Python 2 fallback
      { cmd: 'python', args: ['-m', 'SimpleHTTPServer', port.toString()] },
      // Node.js serve package
      { cmd: 'npx', args: ['serve', '-p', port.toString(), '.'] },
      // Node.js http-server package
      { cmd: 'npx', args: ['http-server', '-p', port.toString()] }
    ];

    let currentOption = 0;

    function tryNextServer() {
      if (currentOption >= serverOptions.length) {
        reject(new Error('No suitable server found. Please install Python or Node.js.'));
        return;
      }

      const option = serverOptions[currentOption];
      console.log(`Trying server option ${currentOption + 1}: ${option.cmd} ${option.args.join(' ')}`);

      const serverProcess = spawn(option.cmd, option.args, {
        cwd: directory,
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: isWindows
      });

      serverProcess.on('error', (error) => {
        console.log(`Server option ${currentOption + 1} failed:`, error.message);
        currentOption++;
        tryNextServer();
      });

      // Wait a moment for the server to start
      setTimeout(() => {
        if (serverProcess.pid) {
          console.log(`Server started successfully with option ${currentOption + 1} on port ${port}`);
          resolve(serverProcess);
        } else {
          currentOption++;
          tryNextServer();
        }
      }, 3000);
    }

    tryNextServer();
  });
}

function cleanupServer(port: number) {
  const serverInfo = activeServers.get(port);
  if (serverInfo) {
    // Kill the server process
    if (serverInfo.process && serverInfo.process.pid) {
      try {
        process.kill(serverInfo.process.pid);
      } catch (error) {
        console.error(`Error killing server process: ${error}`);
      }
    }

    // Clean up the directory
    if (fs.existsSync(serverInfo.directory)) {
      try {
        fs.rmSync(serverInfo.directory, { recursive: true, force: true });
      } catch (error) {
        console.error(`Error cleaning up directory: ${error}`);
      }
    }

    activeServers.delete(port);
  }
}

// Cleanup endpoint
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const port = parseInt(searchParams.get('port') || '');
    
    if (port && activeServers.has(port)) {
      cleanupServer(port);
      return NextResponse.json({ message: 'Server stopped successfully' });
    }
    
    return NextResponse.json({ error: 'Server not found' }, { status: 404 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to stop server' }, { status: 500 });
  }
}
