import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';
import archiver from 'archiver';
import { Readable } from 'stream';
import path from 'path';
import { URL } from 'url';
import fs from 'fs';
import os from 'os';

// Helper function to generate filename from URL
function generateFilenameFromUrl(urlObj: URL): string {
  // Start with hostname
  let filename = urlObj.hostname;

  // Add pathname if it's not just root
  if (urlObj.pathname && urlObj.pathname !== '/') {
    // Remove leading slash and replace slashes with underscores
    const pathPart = urlObj.pathname.substring(1).replace(/\//g, '_');
    filename += '_' + pathPart;
  }

  // Add search params if they exist (common for dynamic pages)
  if (urlObj.search) {
    // Remove the ? and replace special characters
    const searchPart = urlObj.search.substring(1).replace(/[=&]/g, '_');
    filename += '_' + searchPart;
  }

  // Clean up the filename - replace any remaining special characters
  filename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');

  // Remove multiple consecutive underscores
  filename = filename.replace(/_+/g, '_');

  // Remove leading/trailing underscores
  filename = filename.replace(/^_+|_+$/g, '');

  // Limit length to avoid filesystem issues (keep it under 200 chars)
  if (filename.length > 180) {
    filename = filename.substring(0, 180);
  }

  return `${filename}_clone.zip`;
}

interface CloneRequest {
  url: string;
  depth: number;
}

interface CrawledPage {
  url: string;
  html: string;
  assets: Asset[];
  textContent?: string;
  title?: string;
}

interface Asset {
  url: string;
  localPath: string;
  content: Buffer;
  type: 'css' | 'js' | 'image' | 'other';
}

const MAX_SIZE_BYTES = 2 * 1024 * 1024 * 1024; // 2GB
const MAX_PAGES = 100; // Reasonable limit for pages

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    const { url: rawUrl, depth, assetsOnly }: CloneRequest & { assetsOnly?: boolean } = await request.json();

    // Auto-add https:// if no protocol is specified
    let url = rawUrl?.trim();
    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    if (!isValidUrl(url)) {
      return NextResponse.json({ error: 'Invalid URL provided' }, { status: 400 });
    }

    if (depth < 1 || depth > 3) {
      return NextResponse.json({ error: 'Depth must be between 1 and 3' }, { status: 400 });
    }

    // Create temporary directory
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'stoke-clone-'));

    try {
      // Add progress tracking
      const progressCallback = (message: string, progress: number) => {
        console.log(`Progress: ${progress}% - ${message}`);
      };

      const crawledData = await crawlWebsite(url, depth, progressCallback, assetsOnly);

      if (crawledData.length === 0) {
        throw new Error('No pages could be crawled from the provided URL');
      }

      progressCallback('Creating ZIP file...', 90);
      const baseUrl = new URL(url);
      const zipBuffer = await createZipFile(crawledData, tempDir, baseUrl, assetsOnly);

      // Check final size
      if (zipBuffer.length > MAX_SIZE_BYTES) {
        throw new Error(`Generated file is too large (${(zipBuffer.length / 1024 / 1024).toFixed(1)}MB). Maximum size is 2GB.`);
      }

      // Clean up temp directory
      fs.rmSync(tempDir, { recursive: true, force: true });

      progressCallback('Complete!', 100);

      // Generate filename based on URL (reuse existing baseUrl)
      const filename = generateFilenameFromUrl(baseUrl);

      return new NextResponse(zipBuffer, {
        headers: {
          'Content-Type': 'application/zip',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': zipBuffer.length.toString(),
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      });
    } catch (error) {
      // Clean up temp directory on error
      fs.rmSync(tempDir, { recursive: true, force: true });
      throw error;
    }
  } catch (error) {
    console.error('Clone error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to clone website' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        }
      }
    );
  }
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch {
    return false;
  }
}

async function crawlWebsite(
  startUrl: string,
  maxDepth: number,
  progressCallback?: (message: string, progress: number) => void,
  assetsOnly: boolean = false
): Promise<CrawledPage[]> {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const crawledPages: CrawledPage[] = [];
    const visitedUrls = new Set<string>();
    const urlQueue: { url: string; depth: number }[] = [{ url: startUrl, depth: 0 }];
    const baseUrl = new URL(startUrl);
    let totalSize = 0;

    progressCallback?.('Starting website crawl...', 5);

    while (urlQueue.length > 0 && crawledPages.length < MAX_PAGES) {
      const { url: currentUrl, depth } = urlQueue.shift()!;

      if (visitedUrls.has(currentUrl) || depth >= maxDepth) {
        continue;
      }

      visitedUrls.add(currentUrl);

      // Update progress based on pages crawled
      const progress = Math.min(80, 10 + (crawledPages.length / Math.min(MAX_PAGES, 20)) * 70);
      progressCallback?.(`Crawling page ${crawledPages.length + 1}: ${currentUrl}`, progress);

      try {
        const page = await browser.newPage();
        
        // Set user agent to avoid blocking
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // Try with domcontentloaded first, fallback to load if needed
        try {
          await page.goto(currentUrl, {
            waitUntil: 'domcontentloaded',
            timeout: 45000
          });
        } catch (timeoutError) {
          console.log(`Timeout with domcontentloaded, trying with 'load' for ${currentUrl}`);
          await page.goto(currentUrl, {
            waitUntil: 'load',
            timeout: 30000
          });
        }

        // Get page HTML
        const html = await page.content();

        // Extract text content and title for markdown
        const { textContent, title } = await extractTextContent(page, currentUrl);

        // Extract assets
        const assets = await extractAssets(page, currentUrl);

        // In assets-only mode, we only keep media assets
        const filteredAssets = assetsOnly ?
          assets.filter(asset => {
            const ext = asset.url.split('.').pop()?.toLowerCase() || '';
            return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'mp4', 'mp3', 'wav', 'pdf', 'doc', 'docx', 'zip', 'rar'].includes(ext);
          }) : assets;

        // Calculate size
        const pageSize = Buffer.byteLength(html, 'utf8') +
          filteredAssets.reduce((sum, asset) => sum + asset.content.length, 0);
        
        if (totalSize + pageSize > MAX_SIZE_BYTES) {
          const sizeMB = (totalSize / 1024 / 1024).toFixed(1);
          progressCallback?.(`Size limit reached (${sizeMB}MB), stopping crawl`, progress);
          await page.close();
          break;
        }
        
        totalSize += pageSize;

        crawledPages.push({
          url: currentUrl,
          html: assetsOnly ? '' : html, // Don't store HTML in assets-only mode
          assets: filteredAssets,
          textContent,
          title
        });

        // Extract links for next depth level
        if (depth + 1 < maxDepth) {
          const links = await page.evaluate(() => {
            const anchors = Array.from(document.querySelectorAll('a[href]'));
            return anchors.map(a => (a as HTMLAnchorElement).href);
          });

          // Filter and add internal links to queue
          for (const link of links) {
            try {
              const linkUrl = new URL(link);
              // Only follow links on the same domain and avoid fragments/anchors
              if (linkUrl.hostname === baseUrl.hostname &&
                  !visitedUrls.has(link) &&
                  !link.includes('mailto:') &&
                  !link.includes('tel:') &&
                  !link.includes('javascript:') &&
                  !link.includes('#') &&
                  linkUrl.pathname !== '/' || (linkUrl.pathname === '/' && !visitedUrls.has(linkUrl.origin))) {

                // Clean the URL (remove query params and hash for simpler offline browsing)
                const cleanUrl = `${linkUrl.origin}${linkUrl.pathname}`;
                if (!visitedUrls.has(cleanUrl) && cleanUrl !== currentUrl) {
                  urlQueue.push({ url: cleanUrl, depth: depth + 1 });
                }
              }
            } catch {
              // Invalid URL, skip
            }
          }
        }

        await page.close();
      } catch (error) {
        console.error(`Error crawling ${currentUrl}:`, error);

        // If this is the first page and it fails, provide a more helpful error
        if (crawledPages.length === 0 && currentUrl === startUrl) {
          throw new Error(`Failed to access the website: ${error instanceof Error ? error.message : 'Unknown error'}. The website may be down, require authentication, or block automated access.`);
        }

        // Continue with other pages
      }
    }

    return crawledPages;
  } finally {
    await browser.close();
  }
}

async function extractTextContent(page: any, pageUrl: string): Promise<{ textContent: string; title: string }> {
  try {
    const result = await page.evaluate(() => {
      // Get page title
      const title = document.title || '';

      // Remove script and style elements
      const elementsToRemove = document.querySelectorAll('script, style, noscript, iframe');
      elementsToRemove.forEach(el => el.remove());

      // Get main content areas (prioritize semantic elements)
      const contentSelectors = [
        'main',
        'article',
        '[role="main"]',
        '.content',
        '.main-content',
        '#content',
        '#main',
        'body'
      ];

      let contentElement = null;
      for (const selector of contentSelectors) {
        contentElement = document.querySelector(selector);
        if (contentElement) break;
      }

      if (!contentElement) {
        contentElement = document.body;
      }

      // Extract text content
      let textContent = contentElement?.textContent || '';

      // Clean up the text
      textContent = textContent
        .replace(/\s+/g, ' ')  // Replace multiple whitespace with single space
        .replace(/\n\s*\n/g, '\n\n')  // Clean up line breaks
        .trim();

      return { textContent, title };
    });

    return result;
  } catch (error) {
    console.error('Error extracting text content:', error);
    return { textContent: '', title: '' };
  }
}

async function extractAssets(page: any, pageUrl: string): Promise<Asset[]> {
  const assets: Asset[] = [];
  const baseUrl = new URL(pageUrl);

  // Extract CSS files
  const cssUrls = await page.evaluate((pageUrl) => {
    const links = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
    return links.map(link => {
      const href = (link as HTMLLinkElement).href;
      // If it's a relative URL, resolve it against the page URL
      if (href.startsWith('/') || href.startsWith('./') || (!href.startsWith('http') && !href.startsWith('//'))) {
        return new URL(href, pageUrl).href;
      }
      return href;
    });
  }, pageUrl);

  // Wait a bit for dynamic scripts to load
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Extract JS files with multiple approaches
  const jsUrls = await page.evaluate((pageUrl) => {
    // Try multiple selectors to catch all script tags
    const scriptSelectors = [
      'script[src]',
      'script[type="module"][src]',
      'script[type="text/javascript"][src]'
    ];

    const allScripts = new Set<string>();

    scriptSelectors.forEach(selector => {
      const scripts = Array.from(document.querySelectorAll(selector));
      scripts.forEach(script => {
        const src = (script as HTMLScriptElement).src;
        if (src) {
          allScripts.add(src);
        }
      });
    });

    // Also check the HTML source directly for script tags
    const htmlContent = document.documentElement.outerHTML;
    const scriptMatches = htmlContent.match(/<script[^>]*src\s*=\s*["']([^"']+)["'][^>]*>/gi);
    if (scriptMatches) {
      scriptMatches.forEach(match => {
        const srcMatch = match.match(/src\s*=\s*["']([^"']+)["']/i);
        if (srcMatch && srcMatch[1]) {
          allScripts.add(srcMatch[1]);
        }
      });
    }

    // Convert to array and resolve URLs
    return Array.from(allScripts).map(src => {
      // If it's a relative URL, resolve it against the page URL
      if (src.startsWith('/') || src.startsWith('./') || (!src.startsWith('http') && !src.startsWith('//'))) {
        return new URL(src, pageUrl).href;
      }
      return src;
    });
  }, pageUrl);

  // Log JavaScript discovery results
  console.log(`Found ${jsUrls.length} JavaScript files for ${pageUrl}`);

  // Extract images (including background images from CSS)
  const imageUrls = await page.evaluate((pageUrl) => {
    const images = Array.from(document.querySelectorAll('img[src]'));
    const imgSrcs = images.map(img => {
      const src = (img as HTMLImageElement).src;
      // If it's a relative URL, resolve it against the page URL
      if (src.startsWith('/') || src.startsWith('./') || (!src.startsWith('http') && !src.startsWith('//'))) {
        return new URL(src, pageUrl).href;
      }
      return src;
    });

    // Also extract background images
    const elements = Array.from(document.querySelectorAll('*'));
    const bgImages: string[] = [];

    elements.forEach(el => {
      const style = window.getComputedStyle(el);
      const bgImage = style.backgroundImage;
      if (bgImage && bgImage !== 'none') {
        const matches = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/g);
        if (matches) {
          matches.forEach(match => {
            const url = match.replace(/url\(['"]?([^'"]+)['"]?\)/, '$1');
            // Handle both absolute and relative URLs
            if (url.startsWith('http') || url.startsWith('//')) {
              bgImages.push(url);
            } else if (url.startsWith('/') || url.startsWith('./') || (!url.startsWith('data:'))) {
              try {
                bgImages.push(new URL(url, pageUrl).href);
              } catch (e) {
                // Skip invalid URLs
              }
            }
          });
        }
      }
    });

    return [...imgSrcs, ...bgImages];
  }, pageUrl);

  // Extract fonts
  const fontUrls = await page.evaluate(() => {
    const fonts: string[] = [];

    // Extract from CSS @font-face rules
    Array.from(document.styleSheets).forEach(sheet => {
      try {
        Array.from(sheet.cssRules || []).forEach(rule => {
          if (rule instanceof CSSFontFaceRule) {
            const src = rule.style.src;
            if (src) {
              const matches = src.match(/url\(['"]?([^'"]+)['"]?\)/g);
              if (matches) {
                matches.forEach(match => {
                  const url = match.replace(/url\(['"]?([^'"]+)['"]?\)/, '$1');
                  if (url.startsWith('http')) {
                    fonts.push(url);
                  }
                });
              }
            }
          }
        });
      } catch (e) {
        // Cross-origin stylesheets may throw errors
      }
    });

    return fonts;
  });

  // Download assets with better error handling and retries
  const allAssetUrls = [
    ...cssUrls.map(url => ({ url, type: 'css' as const })),
    ...jsUrls.map(url => ({ url, type: 'js' as const })),
    ...imageUrls.map(url => ({ url, type: 'image' as const })),
    ...fontUrls.map(url => ({ url, type: 'other' as const }))
  ];

  // Remove duplicates
  const uniqueAssets = Array.from(new Map(allAssetUrls.map(item => [item.url, item])).values());

  // Log asset discovery summary
  console.log(`Discovered ${uniqueAssets.length} unique assets for ${pageUrl}`);

  for (const { url: assetUrl, type } of uniqueAssets) {
    try {
      const content = await downloadAssetWithRetry(assetUrl);
      if (content) {
        const localPath = generateLocalPath(assetUrl, baseUrl, type);

        assets.push({
          url: assetUrl,
          localPath,
          content,
          type
        });
      }
    } catch (error) {
      console.error(`Failed to download asset ${assetUrl}:`, error);
      // Continue with other assets
    }
  }

  return assets;
}

async function downloadAssetWithRetry(url: string, maxRetries: number = 3): Promise<Buffer | null> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000 // 10 second timeout
      });

      if (response.ok) {
        return Buffer.from(await response.arrayBuffer());
      } else if (response.status === 404) {
        // Don't retry 404s
        return null;
      }
    } catch (error) {
      if (attempt === maxRetries) {
        console.error(`Failed to download ${url} after ${maxRetries} attempts:`, error);
        return null;
      }
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  return null;
}

function generateLocalPath(assetUrl: string, baseUrl: URL, type: string): string {
  try {
    const parsedUrl = new URL(assetUrl);
    let pathname = parsedUrl.pathname;

    // Remove leading slash and handle empty paths
    if (pathname === '/') {
      pathname = 'index.html';
    } else if (pathname.startsWith('/')) {
      pathname = pathname.substring(1);
    }

    // Create directory structure based on asset type
    let directory = '';
    switch (type) {
      case 'css':
        directory = 'assets/css/';
        break;
      case 'js':
        directory = 'assets/js/';
        break;
      case 'image':
        directory = 'assets/images/';
        break;
      case 'other':
        directory = 'assets/fonts/';
        break;
    }

    // If no extension, add appropriate one
    if (!path.extname(pathname)) {
      switch (type) {
        case 'css':
          pathname += '.css';
          break;
        case 'js':
          pathname += '.js';
          break;
        case 'image':
          pathname += '.jpg'; // Default image extension
          break;
        case 'other':
          if (assetUrl.includes('font') || assetUrl.includes('woff') || assetUrl.includes('ttf')) {
            pathname += '.woff2';
          } else {
            pathname += '.bin';
          }
          break;
        default:
          pathname += '.html';
      }
    }

    // Create safe filename - more aggressive sanitization
    let safeName = path.basename(pathname);

    // Replace spaces and special characters with underscores
    safeName = safeName.replace(/[\s\(\)\[\]%20]+/g, '_');

    // Remove any remaining problematic characters
    safeName = safeName.replace(/[^a-zA-Z0-9.\-_]/g, '_');

    // Remove multiple consecutive underscores
    safeName = safeName.replace(/_+/g, '_');

    // Remove leading/trailing underscores
    safeName = safeName.replace(/^_+|_+$/g, '');

    // Ensure we have a valid filename
    if (!safeName || safeName === '.') {
      safeName = `asset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    return directory + safeName;
  } catch {
    // Fallback for invalid URLs
    const extension = getExtensionForType(type);
    return `assets/${type}/asset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${extension}`;
  }
}

function getExtensionForType(type: string): string {
  switch (type) {
    case 'css': return '.css';
    case 'js': return '.js';
    case 'image': return '.jpg';
    case 'other': return '.bin';
    default: return '.html';
  }
}

async function createZipFile(crawledPages: CrawledPage[], tempDir: string, baseUrl: URL, assetsOnly: boolean = false): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', { zlib: { level: 9 } });
    const chunks: Buffer[] = [];

    archive.on('data', (chunk) => chunks.push(chunk));
    archive.on('end', () => resolve(Buffer.concat(chunks)));
    archive.on('error', reject);

    // Keep track of added assets to avoid duplicates
    const addedAssets = new Set<string>();

    // Add a README file with instructions
    const readmeContent = assetsOnly ? `# Website Assets Collection

This ZIP file contains media assets and content extracted from: ${baseUrl.href}

## Contents:
- Images, videos, and media files
- Documents (PDF, DOC, etc.)
- website-content.md - All text content in markdown format for AI analysis

## Assets Only Mode:
This collection contains only media assets and text content.
HTML, CSS, and JavaScript files have been excluded for a lighter download.

Generated on: ${new Date().toISOString()}
` : `# Website Clone

This ZIP file contains a complete offline copy of: ${baseUrl.href}

## How to View:

### ⚠️ IMPORTANT: Use Local Server for Best Results

**Modern browsers block local file access for security reasons.** For the website to display correctly with all CSS, JavaScript, and images, you MUST use a local HTTP server.

### Option 1: Local HTTP Server (Required for Full Functionality)

**Using Python (Recommended):**
\`\`\`bash
# Navigate to the extracted folder, then run:
python3 -m http.server 8000
# Or on Windows:
python -m http.server 8000
\`\`\`

**Using Node.js:**
\`\`\`bash
npx serve .
# or
npx http-server
\`\`\`

**Using PHP:**
\`\`\`bash
php -S localhost:8000
\`\`\`

Then open: **http://localhost:8000**

### Option 2: Direct File Opening (Limited Functionality)
Opening index.html directly in your browser will show:
- ✅ Basic HTML structure
- ❌ No CSS styling (appears unstyled)
- ❌ No JavaScript functionality
- ❌ CORS errors in console

**This is normal browser security behavior and not a bug.**

## Contents:
- HTML pages from the original website
- CSS stylesheets in assets/css/
- JavaScript files in assets/js/
- Images in assets/images/
- Fonts and other assets in assets/fonts/
- website-content.md - All text content in markdown format for AI analysis

## Note:
Some dynamic functionality may not work in this offline copy.
External links will still point to the original websites.

The website-content.md file contains all extracted text content from the website
in a clean markdown format, perfect for AI tools and content analysis.

Generated on: ${new Date().toISOString()}
`;

    archive.append(readmeContent, { name: 'README.md' });

    // Generate and add markdown content file
    const markdownContent = generateMarkdownContent(crawledPages, baseUrl);
    archive.append(markdownContent, { name: 'website-content.md' });

    // Add pages and assets to zip
    for (let i = 0; i < crawledPages.length; i++) {
      const page = crawledPages[i];
      const pageUrl = new URL(page.url);

      // Only add HTML files if not in assets-only mode
      if (!assetsOnly && page.html) {
        let filename = generatePageFilename(pageUrl, i === 0);
        // Process HTML to update asset references
        const processedHtml = updateAssetReferences(page.html, page.assets, page.url);
        archive.append(processedHtml, { name: filename });
      }

      // Add assets (avoiding duplicates)
      for (const asset of page.assets) {
        if (!addedAssets.has(asset.localPath)) {
          addedAssets.add(asset.localPath);
          archive.append(asset.content, { name: asset.localPath });
        }
      }
    }

    archive.finalize();
  });
}

function generatePageFilename(pageUrl: URL, isHomePage: boolean): string {
  if (isHomePage) {
    return 'index.html';
  }

  let pathname = pageUrl.pathname;

  // Remove leading slash
  if (pathname.startsWith('/')) {
    pathname = pathname.substring(1);
  }

  // Handle empty or root paths
  if (pathname === '' || pathname === '/') {
    return 'index.html';
  }

  // If it's a directory path, add index.html
  if (pathname.endsWith('/')) {
    pathname += 'index.html';
  }

  // If no extension, add .html
  if (!path.extname(pathname)) {
    pathname += '.html';
  }

  // Create safe filename
  return pathname.replace(/[^a-zA-Z0-9.\-_/]/g, '_');
}

function updateAssetReferences(html: string, assets: Asset[], pageUrl: string): string {
  let updatedHtml = html;
  const baseUrl = new URL(pageUrl);

  // Create a map for faster lookups
  const assetMap = new Map(assets.map(asset => [asset.url, asset.localPath]));

  // Replace asset URLs in various contexts, but be careful with navigation links
  for (const [originalUrl, localPath] of assetMap) {
    // Escape special regex characters
    const escapedUrl = escapeRegExp(originalUrl);

    // Also try to match relative URLs (e.g., /assets/file.css vs https://domain.com/assets/file.css)
    const urlObj = new URL(originalUrl);
    const relativePath = urlObj.pathname;
    const escapedRelativePath = escapeRegExp(relativePath);

    // Also try to match the original filename (for cases where HTML has different paths)
    const originalFilename = path.basename(relativePath);
    const escapedFilename = escapeRegExp(originalFilename);

    // Handle URL encoding - decode the filename for matching
    const decodedFilename = decodeURIComponent(originalFilename);
    const escapedDecodedFilename = escapeRegExp(decodedFilename);

    // Replace in different contexts:
    // 1. src attributes (images, scripts) - try full URL, relative path, and filename
    updatedHtml = updatedHtml.replace(
      new RegExp(`src=['"]${escapedUrl}['"]`, 'g'),
      `src="${localPath}"`
    );
    updatedHtml = updatedHtml.replace(
      new RegExp(`src=['"]${escapedRelativePath}['"]`, 'g'),
      `src="${localPath}"`
    );
    // Also try to match just the filename (for cases where path differs)
    updatedHtml = updatedHtml.replace(
      new RegExp(`src=['"][^"']*/${escapedFilename}['"]`, 'g'),
      `src="${localPath}"`
    );
    updatedHtml = updatedHtml.replace(
      new RegExp(`src=['"]${escapedFilename}['"]`, 'g'),
      `src="${localPath}"`
    );
    // Also try with decoded filename (handles URL encoding)
    updatedHtml = updatedHtml.replace(
      new RegExp(`src=['"][^"']*/${escapedDecodedFilename}['"]`, 'g'),
      `src="${localPath}"`
    );
    updatedHtml = updatedHtml.replace(
      new RegExp(`src=['"]${escapedDecodedFilename}['"]`, 'g'),
      `src="${localPath}"`
    );

    // 2. href attributes - but only for stylesheets and actual assets, not navigation
    // Check if this is a stylesheet link - try both full URL and relative path
    updatedHtml = updatedHtml.replace(
      new RegExp(`<link[^>]*rel=['"]stylesheet['"][^>]*href=['"]${escapedUrl}['"]`, 'g'),
      (match) => match.replace(originalUrl, localPath)
    );
    updatedHtml = updatedHtml.replace(
      new RegExp(`<link[^>]*rel=['"]stylesheet['"][^>]*href=['"]${escapedRelativePath}['"]`, 'g'),
      (match) => match.replace(relativePath, localPath)
    );

    // 3. CSS url() functions - try both full URL and relative path
    updatedHtml = updatedHtml.replace(
      new RegExp(`url\\(['"]?${escapedUrl}['"]?\\)`, 'g'),
      `url("${localPath}")`
    );
    updatedHtml = updatedHtml.replace(
      new RegExp(`url\\(['"]?${escapedRelativePath}['"]?\\)`, 'g'),
      `url("${localPath}")`
    );

    // 4. CSS @import statements - try both full URL and relative path
    updatedHtml = updatedHtml.replace(
      new RegExp(`@import\\s+['"]${escapedUrl}['"]`, 'g'),
      `@import "${localPath}"`
    );
    updatedHtml = updatedHtml.replace(
      new RegExp(`@import\\s+['"]${escapedRelativePath}['"]`, 'g'),
      `@import "${localPath}"`
    );
  }

  // Handle navigation links separately - convert internal links to work offline
  updatedHtml = processNavigationLinks(updatedHtml, baseUrl);

  // Also process any inline CSS for additional asset references
  updatedHtml = processInlineCSS(updatedHtml, assetMap);

  return updatedHtml;
}

function processNavigationLinks(html: string, baseUrl: URL): string {
  // Find all anchor tags with href attributes
  return html.replace(/<a([^>]*?)href=['"]([^'"]+)['"]([^>]*?)>/g, (match, before, href, after) => {
    try {
      // Skip if it's already a local file reference or external link
      if (href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:') ||
          href.startsWith('javascript:') || href.includes('://') && !href.startsWith(baseUrl.origin)) {
        return match;
      }

      // If it's an internal link on the same domain
      if (href.startsWith('/') || href.startsWith(baseUrl.origin)) {
        const fullUrl = href.startsWith('/') ? baseUrl.origin + href : href;
        const url = new URL(fullUrl);

        // Convert to local filename
        let localPath = url.pathname;
        if (localPath === '/' || localPath === '') {
          localPath = 'index.html';
        } else {
          // Remove leading slash and add .html if needed
          localPath = localPath.substring(1);
          if (!localPath.includes('.')) {
            localPath += '.html';
          }
        }

        return `<a${before}href="${localPath}"${after}>`;
      }

      return match;
    } catch (error) {
      // If URL parsing fails, return original
      return match;
    }
  });
}

function processInlineCSS(html: string, assetMap: Map<string, string>): string {
  // Find and process <style> tags
  return html.replace(/<style[^>]*>([\s\S]*?)<\/style>/gi, (match, cssContent) => {
    let processedCSS = cssContent;

    for (const [originalUrl, localPath] of assetMap) {
      const escapedUrl = escapeRegExp(originalUrl);

      // Replace URLs in CSS
      processedCSS = processedCSS.replace(
        new RegExp(`url\\(['"]?${escapedUrl}['"]?\\)`, 'g'),
        `url("${localPath}")`
      );

      processedCSS = processedCSS.replace(
        new RegExp(`@import\\s+['"]${escapedUrl}['"]`, 'g'),
        `@import "${localPath}"`
      );
    }

    return match.replace(cssContent, processedCSS);
  });
}

function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function generateMarkdownContent(crawledPages: CrawledPage[], baseUrl: URL): string {
  const siteName = baseUrl.hostname.replace('www.', '');
  const timestamp = new Date().toISOString().split('T')[0];

  let markdown = `# Website Content: ${siteName}\n\n`;
  markdown += `**Crawled on:** ${timestamp}\n`;
  markdown += `**Source:** ${baseUrl.href}\n`;
  markdown += `**Pages crawled:** ${crawledPages.length}\n\n`;
  markdown += `---\n\n`;

  // Table of contents
  markdown += `## Table of Contents\n\n`;
  crawledPages.forEach((page, index) => {
    const url = new URL(page.url);
    const pageName = page.title || url.pathname.replace('/', '') || 'Home';
    markdown += `${index + 1}. [${pageName}](#page-${index + 1})\n`;
  });
  markdown += `\n---\n\n`;

  // Page content
  crawledPages.forEach((page, index) => {
    const url = new URL(page.url);
    const pageName = page.title || url.pathname.replace('/', '') || 'Home';

    markdown += `## Page ${index + 1}: ${pageName}\n\n`;
    markdown += `**URL:** ${page.url}\n`;
    if (page.title) {
      markdown += `**Title:** ${page.title}\n`;
    }
    markdown += `\n`;

    if (page.textContent && page.textContent.trim()) {
      // Clean and format the text content
      let content = page.textContent
        .replace(/\s+/g, ' ')  // Normalize whitespace
        .replace(/\. /g, '.\n\n')  // Add paragraph breaks after sentences
        .replace(/\n\n+/g, '\n\n')  // Normalize paragraph breaks
        .trim();

      markdown += `${content}\n\n`;
    } else {
      markdown += `*No text content extracted from this page.*\n\n`;
    }

    markdown += `---\n\n`;
  });

  // Footer
  markdown += `## Extraction Details\n\n`;
  markdown += `- **Total pages:** ${crawledPages.length}\n`;
  markdown += `- **Extraction method:** Automated web scraping\n`;
  markdown += `- **Generated by:** StokeCloner\n`;
  markdown += `- **Date:** ${new Date().toLocaleString()}\n\n`;
  markdown += `*This markdown file contains all text content from the crawled website for easy analysis and processing.*\n`;

  return markdown;
}
