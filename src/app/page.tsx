'use client';

import { useState, useEffect } from 'react';
import { Download, Globe, Settings, AlertCircle, CheckCircle, Loader2, Info, Eye } from 'lucide-react';
import Link from 'next/link';

interface CloneProgress {
  status: 'idle' | 'crawling' | 'downloading' | 'packaging' | 'complete' | 'error';
  message: string;
  progress: number;
  downloadUrl?: string;
  error?: string;
  filename?: string;
}

// Helper function to generate filename from URL
function generateFilenameFromUrl(urlObj: URL): string {
  // Start with hostname
  let filename = urlObj.hostname;

  // Add pathname if it's not just root
  if (urlObj.pathname && urlObj.pathname !== '/') {
    // Remove leading slash and replace slashes with underscores
    const pathPart = urlObj.pathname.substring(1).replace(/\//g, '_');
    filename += '_' + pathPart;
  }

  // Add search params if they exist (common for dynamic pages)
  if (urlObj.search) {
    // Remove the ? and replace special characters
    const searchPart = urlObj.search.substring(1).replace(/[=&]/g, '_');
    filename += '_' + searchPart;
  }

  // Clean up the filename - replace any remaining special characters
  filename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');

  // Remove multiple consecutive underscores
  filename = filename.replace(/_+/g, '_');

  // Remove leading/trailing underscores
  filename = filename.replace(/^_+|_+$/g, '');

  // Limit length to avoid filesystem issues (keep it under 200 chars)
  if (filename.length > 180) {
    filename = filename.substring(0, 180);
  }

  return `${filename}_clone.zip`;
}

export default function Home() {
  const [url, setUrl] = useState('');
  const [depth, setDepth] = useState(1);
  const [assetsOnly, setAssetsOnly] = useState(false);
  const [progress, setProgress] = useState<CloneProgress>({
    status: 'idle',
    message: '',
    progress: 0
  });

  // Handle URL parameter from bookmarklet
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const bookmarkUrl = urlParams.get('url');
    if (bookmarkUrl) {
      setUrl(decodeURIComponent(bookmarkUrl));
      // Clear the URL parameter from the address bar
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  const handleClone = async () => {
    if (!url) return;

    // Auto-add https:// if no protocol is specified
    let processedUrl = url.trim();
    if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://')) {
      processedUrl = 'https://' + processedUrl;
    }

    setProgress({ status: 'crawling', message: 'Starting website crawl...', progress: 10 });

    try {
      const response = await fetch('/api/clone', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: processedUrl, depth, assetsOnly }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || 'Failed to clone website');
      }

      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);

      // Generate filename based on full URL
      const urlObj = new URL(processedUrl);
      const filename = generateFilenameFromUrl(urlObj);

      setProgress({
        status: 'complete',
        message: 'Website cloned successfully!',
        progress: 100,
        downloadUrl,
        filename
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setProgress({
        status: 'error',
        message: errorMessage,
        progress: 0,
        error: errorMessage
      });
    }
  };

  const handleDownload = () => {
    if (progress.downloadUrl) {
      const a = document.createElement('a');
      a.href = progress.downloadUrl;
      
      // Generate filename based on the original URL
      const urlObj = new URL(url);
      const filename = generateFilenameFromUrl(urlObj);
      a.download = filename;
      
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(progress.downloadUrl);

      // Auto-reset form after download
      setTimeout(() => {
        setUrl('');
        setDepth(1);
        setAssetsOnly(false);
        setProgress({ status: 'idle', message: '', progress: 0 });
      }, 1000);
    }
  };

  const handleViewLocally = async () => {
    if (!progress.downloadUrl || !progress.filename) return;

    try {
      // Show loading state
      const originalButton = document.querySelector('[data-view-locally]') as HTMLButtonElement;
      if (originalButton) {
        originalButton.disabled = true;
        originalButton.innerHTML = '<svg class="w-5 h-5 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Starting Local Server...';
      }

      // Download the file first
      const response = await fetch(progress.downloadUrl);
      const blob = await response.blob();

      // Create FormData to send the file to our local server endpoint
      const formData = new FormData();
      formData.append('file', blob, progress.filename);

      // Send to our local server endpoint
      const serverResponse = await fetch('/api/local-server', {
        method: 'POST',
        body: formData
      });

      if (serverResponse.ok) {
        const { url: localUrl } = await serverResponse.json();
        // Open the local server URL
        window.open(localUrl, '_blank');

        // Show success message
        alert(`✅ Local server started!\n\nYour website is now running at: ${localUrl}\n\nThe page should open automatically in a new tab.`);
      } else {
        throw new Error('Failed to start local server');
      }
    } catch (error) {
      console.error('Local server error:', error);

      // Fallback to simple instructions
      const simpleInstructions = `Sorry, automatic server setup failed. Here's the simple manual method:

1. Download the ZIP file (button below)
2. Extract it anywhere on your computer
3. Double-click the "index.html" file

Note: Some styling may not work perfectly, but the content will be visible.`;

      if (confirm(simpleInstructions + '\n\nClick OK to download the ZIP file now.')) {
        handleDownload();
      }
    } finally {
      // Reset button state
      const originalButton = document.querySelector('[data-view-locally]') as HTMLButtonElement;
      if (originalButton) {
        originalButton.disabled = false;
        originalButton.innerHTML = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>View Locally (One Click!)';
      }
    }
  };

  const resetProgress = () => {
    setProgress({ status: 'idle', message: '', progress: 0 });
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <div className="container mx-auto px-6 py-8 pb-24">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold">
              Stoke Cloner
            </h1>
          </div>

          {/* URL Input */}
          <div className="mb-8">
            <label htmlFor="url" className="block text-white text-base font-medium mb-3">
              Website URL
            </label>
            <input
              type="url"
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-4 py-4 bg-slate-800 border border-slate-700 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all"
              disabled={progress.status !== 'idle'}
            />
          </div>

          {/* Crawling Depth */}
          <div className="mb-8">
            <label className="block text-white text-base font-medium mb-4">
              Crawling Depth
            </label>
            <div className="relative">
              <input
                type="range"
                min="1"
                max="3"
                value={depth}
                onChange={(e) => setDepth(Number(e.target.value))}
                className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
                disabled={progress.status !== 'idle'}
              />
              <div className="flex justify-between text-sm text-slate-400 mt-2">
                <span>1</span>
                <span>2</span>
                <span>3</span>
              </div>
            </div>
            <p className="text-sm text-slate-400 mt-3">
              Crawling depth determines how many levels of links from the main page will be included in the clone. Higher depth means a more complete clone but may take longer.
            </p>
          </div>

          {/* Assets Only */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white text-base font-medium">Assets Only</h3>
                <p className="text-sm text-slate-400 mt-1">
                  Download only media files (images, videos, etc.)
                </p>
              </div>
              <button
                onClick={() => setAssetsOnly(!assetsOnly)}
                disabled={progress.status !== 'idle'}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-slate-900 ${
                  assetsOnly ? 'bg-orange-500' : 'bg-slate-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    assetsOnly ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Clone Button */}
          <button
            onClick={handleClone}
            disabled={!url || progress.status !== 'idle'}
            className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-slate-600 disabled:text-slate-400 text-white font-semibold py-4 px-6 rounded-xl transition-colors flex items-center justify-center text-lg"
          >
            {progress.status === 'idle' ? (
              'Clone Website'
            ) : (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Cloning...
              </>
            )}
          </button>

          {/* Progress Section */}
          {progress.status !== 'idle' && (
            <div className="mt-6 bg-slate-800 rounded-xl p-6 border border-slate-700">
              <div className="space-y-4">
                {/* Status Icon and Message */}
                <div className="flex items-center">
                  {progress.status === 'error' ? (
                    <AlertCircle className="w-6 h-6 text-red-400 mr-3" />
                  ) : progress.status === 'complete' ? (
                    <CheckCircle className="w-6 h-6 text-green-400 mr-3" />
                  ) : (
                    <Loader2 className="w-6 h-6 text-orange-500 mr-3 animate-spin" />
                  )}
                  <span className="text-lg font-medium text-white">
                    {progress.message}
                  </span>
                </div>

                {/* Progress Bar */}
                {progress.status !== 'error' && (
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div
                      className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress.progress}%` }}
                    />
                  </div>
                )}

                {/* Error Message */}
                {progress.error && (
                  <div className="bg-red-900/20 border border-red-800 rounded-lg p-4">
                    <p className="text-red-300">{progress.error}</p>
                  </div>
                )}

                {/* Download and View Buttons */}
                {progress.status === 'complete' && progress.downloadUrl && (
                  <div className="space-y-3">
                    <button
                      onClick={handleViewLocally}
                      data-view-locally
                      className="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-4 px-6 rounded-xl transition-colors flex items-center justify-center"
                    >
                      <Eye className="w-5 h-5 mr-2" />
                      View Locally
                    </button>
                    <button
                      onClick={handleDownload}
                      className="w-full bg-slate-700 hover:bg-slate-600 text-white font-semibold py-4 px-6 rounded-xl transition-colors flex items-center justify-center"
                    >
                      <Download className="w-5 h-5 mr-2" />
                      Download ZIP
                    </button>
                  </div>
                )}

                {/* Reset Button */}
                {(progress.status === 'complete' || progress.status === 'error') && (
                  <button
                    onClick={resetProgress}
                    className="w-full bg-slate-700 hover:bg-slate-600 text-white font-semibold py-4 px-6 rounded-xl transition-colors"
                  >
                    Clone Another Website
                  </button>
                )}
              </div>
            </div>
          )}


        </div>
      </div>

      {/* Footer */}
      <div className="fixed bottom-0 left-0 right-0 bg-slate-800 border-t border-slate-700">
        <div className="text-center py-4">
          <Link href="/about" className="text-slate-400 hover:text-orange-500 transition-colors text-sm">
            Technical Details
          </Link>
        </div>
      </div>
    </div>
  );
}
