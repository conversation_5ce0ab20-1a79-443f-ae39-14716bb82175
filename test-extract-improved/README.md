# Website Clone

This is a local copy of a website created with StokeCloner.

## How to view:
1. Extract this ZIP file to a folder
2. Open index.html in your web browser
3. Navigate through the site using the links

## Contents:
- HTML pages from the original website
- CSS stylesheets in assets/css/
- JavaScript files in assets/js/
- Images in assets/images/
- Fonts and other assets in assets/fonts/

## Note:
Some dynamic functionality may not work in this offline copy.
External links will still point to the original websites.

Generated on: 2025-09-20T16:27:11.546Z
